#!/usr/bin/env python3
"""
增强Web API服务启动脚本
启动集成了第二阶段会话管理功能的FastAPI服务
"""

import asyncio
import uvicorn
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 使用字符串导入方式启动服务


def main():
    """主函数"""
    print("🚀 启动增强Web API服务...")
    print("=" * 50)
    print("📋 服务信息:")
    print("  • 名称: LangGraph Agent Enhanced Web API")
    print("  • 版本: 2.1.0")
    print("  • 功能: 完整会话管理 + 智能对话")
    print("  • 地址: http://localhost:8000")
    print("  • 文档: http://localhost:8000/docs")
    print("=" * 50)
    
    # 启动服务
    uvicorn.run(
        "api.enhanced_web_api:enhanced_app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        reload_dirs=[str(project_root)],
        log_level="info"
    )


if __name__ == "__main__":
    main()
