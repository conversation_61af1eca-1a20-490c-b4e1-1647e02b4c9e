<template>
  <div class="enhanced-chat-input">
    <!-- 快捷操作栏 -->
    <div v-if="showQuickActions" class="quick-actions-bar">
      <div class="quick-actions-scroll">
        <button 
          v-for="action in quickActions" 
          :key="action.key"
          class="quick-action-btn"
          @click="executeQuickAction(action)"
          :title="action.description"
        >
          <i :class="action.icon"></i>
          <span>{{ action.label }}</span>
        </button>
      </div>
    </div>

    <!-- 智能提示词（仅在有消息时显示） -->
    <div v-if="hasMessages && !isComposing" class="smart-prompts">
      <div class="prompts-header">
        <span class="prompts-title">智能建议</span>
        <button class="toggle-prompts-btn" @click="togglePrompts">
          <i :class="showPrompts ? 'icon-chevron-up' : 'icon-chevron-down'"></i>
        </button>
      </div>
      
      <div v-if="showPrompts" class="prompts-content">
        <McPrompt
          :list="smartPrompts"
          :direction="'horizontal'"
          :variant="'transparent'"
          @itemClick="onPromptClick"
        />
      </div>
    </div>

    <!-- 输入框容器 -->
    <div class="input-wrapper">
      <!-- 文件上传区域 -->
      <div v-if="dragOver" class="drag-overlay">
        <div class="drag-content">
          <i class="icon-upload"></i>
          <p>拖拽文件到这里上传</p>
        </div>
      </div>

      <!-- 主输入框 -->
      <McInput
        :value="inputValue"
        :maxLength="maxLength"
        :loading="isLoading"
        variant="borderless"
        showCount
        :placeholder="inputPlaceholder"
        @change="onInputChange"
        @submit="onSubmit"
        @keydown="onKeyDown"
        @focus="onInputFocus"
        @blur="onInputBlur"
        class="main-input"
      >
        <template #extra>
          <div class="input-footer">
            <div class="input-footer-left">
              <!-- 文件上传 -->
              <div class="input-action-item" @click="triggerFileUpload">
                <i class="icon-attachment"></i>
                <span>附件</span>
                <input 
                  ref="fileInput"
                  type="file"
                  multiple
                  @change="onFileSelect"
                  style="display: none;"
                />
              </div>
              
              <!-- 分隔线 -->
              <span class="input-divider"></span>
              
              <!-- 语音输入 -->
              <div 
                class="input-action-item voice-input"
                :class="{ active: isRecording }"
                @click="toggleVoiceInput"
              >
                <i :class="isRecording ? 'icon-mic-off' : 'icon-mic'"></i>
                <span>{{ isRecording ? '停止录音' : '语音输入' }}</span>
              </div>
              
              <!-- 分隔线 -->
              <span class="input-divider"></span>
              
              <!-- 字符计数 -->
              <span class="char-count" :class="{ warning: isNearLimit }">
                {{ inputValue.length }}/{{ maxLength }}
              </span>
            </div>
            
            <div class="input-footer-right">
              <!-- 快捷操作切换 -->
              <button 
                class="toggle-actions-btn"
                @click="toggleQuickActions"
                :class="{ active: showQuickActions }"
                title="快捷操作"
              >
                <i class="icon-magic"></i>
              </button>
              
              <!-- 清空输入 -->
              <button 
                class="clear-input-btn"
                @click="clearInput" 
                :disabled="!inputValue"
                title="清空输入"
              >
                <i class="icon-clear"></i>
              </button>
              
              <!-- 发送按钮 -->
              <button 
                class="send-btn"
                @click="onSubmit"
                :disabled="!canSend"
                :class="{ pulse: hasContent && !isLoading }"
                title="发送消息 (Ctrl+Enter)"
              >
                <i v-if="isLoading" class="icon-loading spinning"></i>
                <i v-else class="icon-send"></i>
              </button>
            </div>
          </div>
        </template>
      </McInput>
    </div>

    <!-- 输入提示 -->
    <div v-if="showInputHints" class="input-hints">
      <div class="hint-item">
        <kbd>Ctrl</kbd> + <kbd>Enter</kbd> 发送消息
      </div>
      <div class="hint-item">
        <kbd>Shift</kbd> + <kbd>Enter</kbd> 换行
      </div>
      <div class="hint-item">
        <kbd>/</kbd> 快捷命令
      </div>
    </div>

    <!-- 上传文件预览 -->
    <div v-if="uploadedFiles.length > 0" class="uploaded-files">
      <div class="files-header">
        <span>已上传文件 ({{ uploadedFiles.length }})</span>
        <button class="clear-files-btn" @click="clearFiles">
          <i class="icon-close"></i>
        </button>
      </div>
      <div class="files-list">
        <div 
          v-for="(file, index) in uploadedFiles" 
          :key="index"
          class="file-item"
        >
          <div class="file-info">
            <i class="file-icon" :class="getFileIcon(file.type)"></i>
            <span class="file-name">{{ file.name }}</span>
            <span class="file-size">{{ formatFileSize(file.size) }}</span>
          </div>
          <button class="remove-file-btn" @click="removeFile(index)">
            <i class="icon-close"></i>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const inputValue = ref('');
const isLoading = ref(false);
const isComposing = ref(false);
const showQuickActions = ref(false);
const showPrompts = ref(true);
const showInputHints = ref(false);
const isRecording = ref(false);
const dragOver = ref(false);
const uploadedFiles = ref<File[]>([]);
const fileInput = ref<HTMLInputElement>();
const maxLength = ref(4000);

// 计算属性
const hasMessages = computed(() => chatStore.messages.length > 0);
const hasContent = computed(() => inputValue.value.trim().length > 0);
const canSend = computed(() => hasContent.value && !isLoading.value);
const isNearLimit = computed(() => inputValue.value.length > maxLength.value * 0.9);

const inputPlaceholder = computed(() => {
  if (isRecording.value) return '正在录音中...';
  if (hasMessages.value) return '继续对话...';
  return '输入消息开始对话...';
});

// 快捷操作配置
const quickActions = ref([
  { key: 'summarize', label: '总结', icon: 'icon-summary', description: '总结上述内容' },
  { key: 'translate', label: '翻译', icon: 'icon-translate', description: '翻译为英文' },
  { key: 'explain', label: '解释', icon: 'icon-explain', description: '详细解释' },
  { key: 'continue', label: '继续', icon: 'icon-continue', description: '请继续' },
  { key: 'improve', label: '改进', icon: 'icon-improve', description: '改进建议' },
  { key: 'code', label: '代码', icon: 'icon-code', description: '生成代码' },
]);

// 智能提示词
const smartPrompts = computed(() => {
  const basePrompts = [
    { text: '请详细解释', value: '请详细解释' },
    { text: '给出示例', value: '请给出具体示例' },
    { text: '总结要点', value: '请总结主要要点' },
  ];
  
  if (hasMessages.value) {
    return [
      ...basePrompts,
      { text: '继续说明', value: '请继续详细说明' },
      { text: '换个角度', value: '请从另一个角度分析' },
    ];
  }
  
  return basePrompts;
});

// 方法
const onInputChange = (value: string) => {
  inputValue.value = value;
  
  // 检测是否输入快捷命令
  if (value.startsWith('/')) {
    showInputHints.value = true;
  } else {
    showInputHints.value = false;
  }
};

const onSubmit = async () => {
  if (!canSend.value) return;
  
  const message = inputValue.value.trim();
  if (!message) return;
  
  isLoading.value = true;
  
  try {
    // 发送消息
    await chatStore.sendMessage(message);
    
    // 清空输入
    inputValue.value = '';
    uploadedFiles.value = [];
    
  } catch (error) {
    console.error('发送消息失败:', error);
  } finally {
    isLoading.value = false;
  }
};

const onKeyDown = (event: KeyboardEvent) => {
  // Ctrl+Enter 发送
  if (event.ctrlKey && event.key === 'Enter') {
    event.preventDefault();
    onSubmit();
  }
  
  // Shift+Enter 换行（默认行为）
  if (event.shiftKey && event.key === 'Enter') {
    return;
  }
  
  // 快捷命令处理
  if (event.key === '/' && !inputValue.value) {
    showInputHints.value = true;
  }
};

const onInputFocus = () => {
  isComposing.value = true;
};

const onInputBlur = () => {
  setTimeout(() => {
    isComposing.value = false;
    showInputHints.value = false;
  }, 200);
};

const clearInput = () => {
  inputValue.value = '';
  uploadedFiles.value = [];
};

const toggleQuickActions = () => {
  showQuickActions.value = !showQuickActions.value;
};

const togglePrompts = () => {
  showPrompts.value = !showPrompts.value;
};

const executeQuickAction = (action: any) => {
  const actionTexts = {
    summarize: '请总结上述内容的主要要点',
    translate: '请将上述内容翻译为英文',
    explain: '请详细解释上述内容',
    continue: '请继续',
    improve: '请提供改进建议',
    code: '请生成相关代码示例'
  };
  
  inputValue.value = actionTexts[action.key as keyof typeof actionTexts] || action.label;
};

const onPromptClick = (prompt: any) => {
  inputValue.value = prompt.value;
};

// 文件处理
const triggerFileUpload = () => {
  fileInput.value?.click();
};

const onFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (target.files) {
    uploadedFiles.value.push(...Array.from(target.files));
  }
};

const removeFile = (index: number) => {
  uploadedFiles.value.splice(index, 1);
};

const clearFiles = () => {
  uploadedFiles.value = [];
};

const getFileIcon = (type: string) => {
  if (type.startsWith('image/')) return 'icon-image';
  if (type.startsWith('video/')) return 'icon-video';
  if (type.startsWith('audio/')) return 'icon-audio';
  if (type.includes('pdf')) return 'icon-pdf';
  if (type.includes('word')) return 'icon-word';
  if (type.includes('excel')) return 'icon-excel';
  return 'icon-file';
};

const formatFileSize = (bytes: number) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 语音输入
const toggleVoiceInput = () => {
  if (isRecording.value) {
    stopRecording();
  } else {
    startRecording();
  }
};

const startRecording = () => {
  // 这里实现语音录制逻辑
  isRecording.value = true;
  console.log('开始录音');
};

const stopRecording = () => {
  // 这里实现停止录制逻辑
  isRecording.value = false;
  console.log('停止录音');
};

// 拖拽上传
const handleDragOver = (event: DragEvent) => {
  event.preventDefault();
  dragOver.value = true;
};

const handleDragLeave = () => {
  dragOver.value = false;
};

const handleDrop = (event: DragEvent) => {
  event.preventDefault();
  dragOver.value = false;
  
  if (event.dataTransfer?.files) {
    uploadedFiles.value.push(...Array.from(event.dataTransfer.files));
  }
};

// 生命周期
onMounted(() => {
  document.addEventListener('dragover', handleDragOver);
  document.addEventListener('dragleave', handleDragLeave);
  document.addEventListener('drop', handleDrop);
});

onUnmounted(() => {
  document.removeEventListener('dragover', handleDragOver);
  document.removeEventListener('dragleave', handleDragLeave);
  document.removeEventListener('drop', handleDrop);
});
</script>

<style scoped>
/* 样式将在对应的CSS文件中定义 */
</style>
