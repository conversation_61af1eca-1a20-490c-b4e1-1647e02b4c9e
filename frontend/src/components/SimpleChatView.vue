<template>
  <div class="simple-chat-view">
    <!-- 使用MateChat官方布局组件 -->
    <McLayout>
      <!-- 左侧历史会话面板 -->
      <McLayoutAside width="280px" class="chat-aside">
        <div class="aside-header">
          <img src="https://matechat.gitcode.com/logo.svg" alt="MateChat" class="logo" />
          <span class="title">AI智能助手</span>
        </div>
        
        <!-- 新建会话按钮 -->
        <div class="new-session-container">
          <button @click="createNewSession" class="new-session-btn">
            <i class="icon-add"></i>
            新建会话
          </button>
        </div>
        
        <!-- 历史会话列表 -->
        <div class="session-list-container">
          <McList 
            :data="sessionListData"
            @select="onSessionSelect"
            class="session-list"
          >
            <template #item="{ item }">
              <div class="session-item" :class="{ 'active': item.active }">
                <div class="session-content">
                  <div class="session-title">{{ item.label }}</div>
                  <div class="session-time">{{ formatTime(item.lastActiveTime) }}</div>
                </div>
                <div class="session-actions">
                  <button @click.stop="deleteSession(item.value)" class="delete-btn" title="删除">
                    <i class="icon-delete"></i>
                  </button>
                </div>
              </div>
            </template>
          </McList>
        </div>
      </McLayoutAside>

      <!-- 右侧聊天内容区域 -->
      <McLayoutContent class="chat-content">
        <!-- 头部 -->
        <McLayoutHeader class="chat-header">
          <McHeader 
            :logoImg="'https://matechat.gitcode.com/logo.svg'" 
            :title="currentSessionTitle || 'AI智能助手'"
          />
        </McLayoutHeader>

        <!-- 聊天消息区域 -->
        <div class="chat-messages" v-if="chatStore.startChat">
          <div class="messages-container" ref="messagesContainer">
            <McBubble 
              v-for="(message, index) in chatStore.messages" 
              :key="index"
              :content="message.content"
              :align="message.role === 'user' ? 'right' : 'left'"
              :avatar="message.role === 'user' ? '' : 'https://matechat.gitcode.com/logo.svg'"
            />
          </div>
        </div>

        <!-- 欢迎界面 -->
        <div class="welcome-container" v-else>
          <McIntroduction
            :logoImg="'https://matechat.gitcode.com/logo.svg'"
            :title="'AI智能助手'"
            :subTitle="'Hi，欢迎使用智能助手'"
            :description="welcomeDescription"
          />
        </div>

        <!-- 底部输入区域 -->
        <McLayoutSender class="chat-sender">
          <McInput 
            v-model="inputValue" 
            :maxLength="2000" 
            showCount
            placeholder="请输入您的问题..."
            @keyup.enter="sendMessage"
          />
        </McLayoutSender>
      </McLayoutContent>
    </McLayout>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import { 
  McLayout, 
  McLayoutAside, 
  McLayoutContent, 
  McLayoutHeader, 
  McLayoutSender,
  McHeader,
  McList,
  McBubble,
  McIntroduction,
  McInput
} from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const inputValue = ref('');
const messagesContainer = ref<HTMLElement>();

// 欢迎描述
const welcomeDescription = [
  '我可以帮助您解答问题、编写代码、分析文档等。',
  '请在下方输入框中输入您的问题，我会尽力为您提供帮助。'
];

// 计算属性
const sessionListData = computed(() => {
  return chatStore.sessions.map(session => ({
    label: session.title || '新对话',
    value: session.id,
    lastActiveTime: session.lastActiveTime || new Date(),
    active: session.id === chatStore.currentSessionId
  }));
});

const currentSessionTitle = computed(() => {
  const currentSession = chatStore.sessions.find(s => s.id === chatStore.currentSessionId);
  return currentSession?.title || '新对话';
});

// 方法
const createNewSession = () => {
  chatStore.createSession();
};

const onSessionSelect = (item: any) => {
  chatStore.selectSession(item.value);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    chatStore.deleteSession(sessionId);
  }
};

const sendMessage = async () => {
  if (!inputValue.value.trim()) return;
  
  const message = inputValue.value.trim();
  inputValue.value = '';
  
  // 发送消息到store
  await chatStore.sendMessage(message);
  
  // 滚动到底部
  await nextTick();
  scrollToBottom();
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString('zh-CN');
};

// 生命周期
onMounted(() => {
  // 初始化时滚动到底部
  nextTick(() => {
    scrollToBottom();
  });
});
</script>

<style scoped lang="scss">
.simple-chat-view {
  height: 100vh;
  width: 100%;
}

.chat-aside {
  background: var(--devui-base-bg-dark, #f8f9fa);
  border-right: 1px solid var(--devui-dividing-line, #e5e5e5);
  display: flex;
  flex-direction: column;
}

.aside-header {
  padding: 16px;
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo {
  width: 32px;
  height: 32px;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: var(--devui-text, #333);
}

.new-session-container {
  padding: 16px;
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
}

.new-session-btn {
  width: 100%;
  padding: 12px;
  background: var(--devui-brand, #5e7ce0);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;

  &:hover {
    background: var(--devui-brand-hover, #4a6bc7);
  }

  i {
    font-size: 16px;
  }
}

.session-list-container {
  flex: 1;
  overflow: hidden;
  padding: 8px;
}

.session-list {
  height: 100%;
}

.session-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 4px;

  &:hover {
    background: var(--devui-list-item-hover-bg, #f2f5fc);
  }

  &.active {
    background: var(--devui-brand-active-bg, #e8f2ff);
    border-left: 3px solid var(--devui-brand, #5e7ce0);
  }
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--devui-text, #333);
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-time {
  font-size: 12px;
  color: var(--devui-aide-text, #8a8e99);
}

.session-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.session-item:hover .session-actions {
  opacity: 1;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  color: var(--devui-danger, #f66f6a);
  
  &:hover {
    background: var(--devui-danger-bg, #ffeaea);
  }
}

.chat-content {
  display: flex;
  flex-direction: column;
  height: 100vh;
}

.chat-header {
  border-bottom: 1px solid var(--devui-dividing-line, #e5e5e5);
}

.chat-messages {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
}

.welcome-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 32px;
}

.chat-sender {
  border-top: 1px solid var(--devui-dividing-line, #e5e5e5);
  padding: 16px;
}
</style>
