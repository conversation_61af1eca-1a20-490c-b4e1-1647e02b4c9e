<template>
  <!-- 全屏布局容器 - 模仿官方演示页面 -->
  <div class="matechat-app">
    <!-- 顶部导航栏 -->
    <div class="app-header">
      <div class="header-left">
        <img src="https://matechat.gitcode.com/logo.svg" alt="MateChat" class="app-logo" />
        <span class="app-title">MateChat</span>
      </div>
      <div class="header-right">
        <div class="header-nav">
          <div class="nav-item active">
            <img src="https://matechat.gitcode.com/vue-starter/chat-icon.svg" alt="对话" class="nav-icon" />
            <span>对话</span>
          </div>
        </div>
        <div class="header-actions">
          <span class="lang-switch">EN</span>
        </div>
      </div>
    </div>

    <!-- 主体内容区域 -->
    <div class="app-body">
      <!-- 左侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h3 class="sidebar-title">对话历史</h3>
          <div class="sidebar-search">
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTcuMzMzMzMgMTIuNjY2N0MxMC4yNzg5IDEyLjY2NjcgMTIuNjY2NyAxMC4yNzg5IDEyLjY2NjcgNy4zMzMzM0MxMi42NjY3IDQuMzg3ODEgMTAuMjc4OSAyIDcuMzMzMzMgMkM0LjM4NzgxIDIgMiA0LjM4NzgxIDIgNy4zMzMzM0MyIDEwLjI3ODkgNC4zODc4MSAxMi42NjY3IDcuMzMzMzMgMTIuNjY2N1oiIHN0cm9rZT0iIzk5OTk5OSIgc3Ryb2tlLXdpZHRoPSIxLjMzMzMzIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTE0IDE0TDExLjEgMTEuMSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjEuMzMzMzMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K" alt="搜索" class="search-icon" />
            <input type="text" placeholder="搜索对话..." class="search-input" />
          </div>
        </div>

        <div class="sidebar-content">
          <!-- 会话列表 -->
          <div class="session-list" v-if="sessionListData.length > 0">
            <div
              v-for="session in sessionListData"
              :key="session.value"
              class="session-item"
              :class="{ 'active': session.active }"
              @click="onSessionSelect(session)"
            >
              <div class="session-content">
                <div class="session-title">{{ session.label }}</div>
                <div class="session-time">{{ formatTime(session.lastActiveTime) }}</div>
              </div>
              <div class="session-actions">
                <button @click.stop="deleteSession(session.value)" class="delete-btn" title="删除">
                  <svg width="14" height="14" viewBox="0 0 14 14" fill="none">
                    <path d="M1.75 3.5H12.25M5.25 6.125V10.5M8.75 6.125V10.5M2.625 3.5L3.5 12.25H10.5L11.375 3.5M5.25 3.5V1.75C5.25 1.40482 5.52982 1.125 5.875 1.125H8.125C8.47018 1.125 8.75 1.40482 8.75 1.75V3.5" stroke="currentColor" stroke-width="1.16667" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                </button>
              </div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div class="empty-state" v-else>
            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTMyIDU2QzQ1LjI1NDggNTYgNTYgNDUuMjU0OCA1NiAzMkM1NiAxOC43NDUyIDQ1LjI1NDggOCAzMiA4QzE4Ljc0NTIgOCA4IDE4Ljc0NTIgOCAzMkM4IDQ1LjI1NDggMTguNzQ1MiA1NiAzMiA1NloiIHN0cm9rZT0iI0RERERERCIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPHBhdGggZD0iTTMyIDI0VjMyTDM2IDM2IiBzdHJva2U9IiNEREREREQiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=" alt="无数据" class="empty-icon" />
            <span class="empty-text">无数据</span>
          </div>
        </div>
      </div>

      <!-- 右侧主内容区 -->
      <div class="main-content">
        <!-- 聊天消息区域 -->
        <div class="chat-area" v-if="chatStore.startChat">
          <div class="messages-container" ref="messagesContainer">
            <McBubble
              v-for="(message, index) in chatStore.messages"
              :key="index"
              :content="message.content"
              :align="message.role === 'user' ? 'right' : 'left'"
              :avatar="message.role === 'user' ? '' : 'https://matechat.gitcode.com/logo.svg'"
            />
          </div>
        </div>

        <!-- 欢迎界面 -->
        <div class="welcome-area" v-else>
          <div class="welcome-content">
            <McIntroduction
              :logoImg="'https://matechat.gitcode.com/vue-starter/assets/logo2x-fCriM3V0.svg'"
              :title="'langgraph agent'"
              :subTitle="'langgraph agent 可以辅助研发人员编码、查询知识和相关作业信息、编写文档等。'"
              :description="welcomeDescription"
            />

            <!-- 猜你想问 -->
            <div class="quick-questions">
              <div class="questions-header">
                <span class="questions-title">猜你想问</span>
                <button class="refresh-btn">
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M13.65 6.35C13.4 4.28 12.28 2.4 10.6 1.2C8.92 0 6.84 -0.32 4.89 0.24C2.94 0.8 1.26 2.12 0.24 3.89C-0.78 5.66 -0.78 7.74 0.24 9.51" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M2.35 9.65C2.6 11.72 3.72 13.6 5.4 14.8C7.08 16 9.16 16.32 11.11 15.76C13.06 15.2 14.74 13.88 15.76 12.11C16.78 10.34 16.78 8.26 15.76 6.49" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <span>换一批</span>
                </button>
              </div>
              <div class="questions-list">
                <button class="question-item" @click="sendQuickMessage('帮我写一篇文章')">帮我写一篇文章</button>
                <button class="question-item" @click="sendQuickMessage('你可以帮我做些什么？')">你可以帮我做些什么？</button>
                <button class="question-item" @click="sendQuickMessage('帮我写一个快速排序')">帮我写一个快速排序</button>
                <button class="question-item" @click="sendQuickMessage('使用 js 格式化时间')">使用 js 格式化时间</button>
              </div>
            </div>
          </div>
        </div>

        <!-- 底部输入区域 -->
        <div class="input-area">
          <div class="input-toolbar">
            <div class="model-selector">
              <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggMTRDMTEuMzEzNyAxNCA0IDExLjMxMzcgMTQgOEMxNCA0LjY4NjI5IDExLjMxMzcgMiA4IDJDNC42ODYyOSAyIDIgNC42ODYyOSAyIDhDMiAxMS4zMTM3IDQuNjg2MjkgMTQgOCAxNFoiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjEuMzMzMzMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8cGF0aCBkPSJNOCA2VjhMMTAgMTAiIHN0cm9rZT0iY3VycmVudENvbG9yIiBzdHJva2Utd2lkdGg9IjEuMzMzMzMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K" alt="模型" class="model-icon" />
              <span class="model-name">deepseek-ai/DeepSeek-R1</span>
              <svg width="12" height="12" viewBox="0 0 12 12" fill="none" class="dropdown-icon">
                <path d="M3 4.5L6 7.5L9 4.5" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </div>

          </div>

          <div class="input-container">
            <div class="input-wrapper">
              <textarea
                v-model="inputValue"
                placeholder="请输入您的问题，并按Enter发送，按Shift + Enter换行"
                class="message-input"
                @keydown="handleKeydown"
                rows="1"
              ></textarea>

              <div class="input-actions">
                <div class="action-buttons">
                  <span class="char-count">{{ inputValue.length }}/2000</span>
                </div>

                <button
                  class="send-btn"
                  :disabled="!inputValue.trim()"
                  @click="sendMessage"
                >
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none">
                    <path d="M14.6667 1.33333L7.33333 8.66667" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M14.6667 1.33333L10 14.6667L7.33333 8.66667L1.33333 6L14.6667 1.33333Z" stroke="currentColor" stroke-width="1.33333" stroke-linecap="round" stroke-linejoin="round"/>
                  </svg>
                  <span>发送</span>
                </button>
              </div>
            </div>
          </div>

          <div class="input-footer">
            <span class="disclaimer">内容由AI生成，无法确保准确性和完整性，仅供参考</span>
            <a href="#" class="privacy-link">隐私声明</a>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted } from 'vue';
import { 
  McLayout, 
  McLayoutAside, 
  McLayoutContent, 
  McLayoutHeader, 
  McLayoutSender,
  McHeader,
  McList,
  McBubble,
  McIntroduction,
  McInput
} from '@matechat/core';
import { useChatStore } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const inputValue = ref('');
const messagesContainer = ref<HTMLElement>();

// 欢迎描述
const welcomeDescription = [
  '我可以帮助您解答问题、编写代码、分析文档等。',
  '请在下方输入框中输入您的问题，我会尽力为您提供帮助。'
];

// 计算属性
const sessionListData = computed(() => {
  return chatStore.sessions.map(session => ({
    label: session.title || '新对话',
    value: session.id,
    lastActiveTime: session.lastActiveTime || new Date(),
    active: session.id === chatStore.currentSessionId
  }));
});

const currentSessionTitle = computed(() => {
  const currentSession = chatStore.sessions.find(s => s.id === chatStore.currentSessionId);
  return currentSession?.title || '新对话';
});

// 方法
const createNewSession = () => {
  chatStore.createSession();
};

const onSessionSelect = (item: any) => {
  chatStore.selectSession(item.value);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后无法恢复。')) {
    chatStore.deleteSession(sessionId);
  }
};

const sendMessage = async () => {
  if (!inputValue.value.trim()) return;

  const message = inputValue.value.trim();
  inputValue.value = '';

  // 发送消息到store
  await chatStore.sendMessage(message);

  // 滚动到底部
  await nextTick();
  scrollToBottom();
};

const sendQuickMessage = async (message: string) => {
  inputValue.value = message;
  await sendMessage();
};

const handleKeydown = (event: KeyboardEvent) => {
  if (event.key === 'Enter' && !event.shiftKey) {
    event.preventDefault();
    sendMessage();
  }
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const formatTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes}分钟前`;
  if (hours < 24) return `${hours}小时前`;
  if (days < 7) return `${days}天前`;
  return date.toLocaleDateString('zh-CN');
};

// 生命周期
onMounted(() => {
  // 初始化时滚动到底部
  nextTick(() => {
    scrollToBottom();
  });
});
</script>

<style scoped lang="scss">
/* 全局重置和全屏布局 */
.matechat-app {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8f9fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  overflow: hidden;
}

/* 顶部导航栏 */
.app-header {
  height: 60px;
  background: white;
  border-bottom: 1px solid #e5e5e5;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  flex-shrink: 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.app-logo {
  width: 32px;
  height: 32px;
}

.app-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-nav {
  display: flex;
  gap: 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;

  &.active {
    background: #f0f7ff;
    color: #1890ff;
  }

  &:hover:not(.active) {
    background: #f5f5f5;
  }
}

.nav-icon {
  width: 16px;
  height: 16px;
}

.lang-switch {
  padding: 6px 12px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #666;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

/* 主体内容区域 */
.app-body {
  flex: 1;
  display: flex;
  overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
  width: 280px;
  background: white;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
}

.sidebar-header {
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.sidebar-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0 0 16px 0;
}

.sidebar-search {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  width: 16px;
  height: 16px;
  z-index: 1;
}

.search-input {
  width: 100%;
  height: 36px;
  padding: 0 12px 0 36px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  font-size: 14px;
  background: #fafafa;

  &::placeholder {
    color: #999;
  }

  &:focus {
    outline: none;
    border-color: #1890ff;
    background: white;
  }
}

.sidebar-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 会话列表 */
.session-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px 16px;
}

.session-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
  margin-bottom: 4px;

  &:hover {
    background: #f5f5f5;

    .session-actions {
      opacity: 1;
    }
  }

  &.active {
    background: #e6f7ff;
    border-left: 3px solid #1890ff;

    .session-title {
      color: #1890ff;
      font-weight: 600;
    }
  }
}

.session-content {
  flex: 1;
  min-width: 0;
}

.session-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.session-time {
  font-size: 12px;
  color: #999;
}

.session-actions {
  opacity: 0;
  transition: opacity 0.2s;
}

.delete-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  color: #999;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover {
    background: #fff2f0;
    color: #ff4d4f;
  }
}

/* 无数据状态 */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  color: #999;
}

.empty-icon {
  width: 64px;
  height: 64px;
  opacity: 0.5;
}

.empty-text {
  font-size: 14px;
}

/* 右侧主内容区 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  background: white;
  overflow: hidden;
}

/* 聊天消息区域 */
.chat-area {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

/* 欢迎界面 */
.welcome-area {
  flex: 1;
  overflow-y: auto;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.welcome-content {
  max-width: 800px;
  width: 100%;
  text-align: center;
}

/* 猜你想问 */
.quick-questions {
  margin-top: 40px;
}

.questions-header {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 20px;
}

.questions-title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.refresh-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: none;
  border: 1px solid #e5e5e5;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.questions-list {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  justify-content: center;
}

.question-item {
  padding: 12px 20px;
  background: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  color: #333;
  transition: all 0.2s;

  &:hover {
    background: #e6f7ff;
    border-color: #1890ff;
    color: #1890ff;
  }
}

/* 底部输入区域 */
.input-area {
  flex-shrink: 0;
  padding: 20px;
  border-top: 1px solid #f0f0f0;
}

.input-toolbar {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 12px;
}

.model-selector {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f8f9fa;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #333;

  &:hover {
    border-color: #1890ff;
  }
}

.model-icon {
  width: 16px;
  height: 16px;
}

.model-name {
  font-weight: 500;
}

.dropdown-icon {
  margin-left: 4px;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: none;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  transition: all 0.2s;

  &:hover {
    border-color: #1890ff;
    color: #1890ff;
  }
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* 输入容器 */
.input-container {
  border: 1px solid #e5e5e5;
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.2s;

  &:focus-within {
    border-color: #1890ff;
    box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
  }
}

.input-wrapper {
  display: flex;
  flex-direction: column;
}

.message-input {
  width: 100%;
  min-height: 60px;
  max-height: 200px;
  padding: 16px;
  border: none;
  outline: none;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  font-family: inherit;

  &::placeholder {
    color: #999;
  }
}

.input-actions {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: #fafafa;
  border-top: 1px solid #f0f0f0;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 16px;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 13px;
  color: #666;
  transition: all 0.2s;

  &:hover {
    color: #1890ff;
  }
}

.char-count {
  font-size: 12px;
  color: #999;
}

.send-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;

  &:hover:not(:disabled) {
    background: #40a9ff;
  }

  &:disabled {
    background: #d9d9d9;
    cursor: not-allowed;
  }
}

/* 输入区域底部 */
.input-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-top: 12px;
  font-size: 12px;
  color: #999;
}

.disclaimer {
  text-align: center;
}

.privacy-link {
  color: #1890ff;
  text-decoration: none;

  &:hover {
    text-decoration: underline;
  }
}
</style>
