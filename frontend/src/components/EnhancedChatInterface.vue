<template>
  <div class="enhanced-chat-interface">
    <!-- 聊天头部 -->
    <div class="chat-header">
      <div class="session-info">
        <h3 class="session-title">{{ currentSessionTitle }}</h3>
        <div class="session-meta">
          <span class="message-count">{{ messageCount }} 条消息</span>
          <span class="last-activity">{{ formatLastActivity(lastActivity) }}</span>
        </div>
      </div>
      
      <!-- 快捷操作 -->
      <div class="quick-actions">
        <button 
          class="action-btn" 
          @click="toggleSearchMode"
          :class="{ active: searchMode }"
          title="搜索消息"
        >
          <i class="icon-search"></i>
        </button>
        
        <button 
          class="action-btn" 
          @click="exportSession"
          title="导出会话"
        >
          <i class="icon-download"></i>
        </button>
        
        <button 
          class="action-btn" 
          @click="clearSession"
          title="清空会话"
        >
          <i class="icon-trash"></i>
        </button>
        
        <button 
          class="action-btn" 
          @click="toggleFullscreen"
          :class="{ active: isFullscreen }"
          title="全屏模式"
        >
          <i class="icon-fullscreen"></i>
        </button>
      </div>
    </div>

    <!-- 搜索栏 -->
    <div v-if="searchMode" class="search-bar">
      <div class="search-input-wrapper">
        <input 
          v-model="searchQuery"
          type="text"
          placeholder="搜索消息内容..."
          class="search-input"
          @input="onSearchInput"
          @keyup.enter="performSearch"
        />
        <button class="search-btn" @click="performSearch">
          <i class="icon-search"></i>
        </button>
      </div>
      
      <div v-if="searchResults.length > 0" class="search-results-info">
        找到 {{ searchResults.length }} 条结果
        <button class="clear-search-btn" @click="clearSearch">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>

    <!-- 消息列表容器 -->
    <div 
      ref="messagesContainer" 
      class="messages-container"
      :class="{ 'search-active': searchMode && searchResults.length > 0 }"
      @scroll="onScroll"
    >
      <!-- 加载更多历史消息 -->
      <div v-if="hasMoreHistory" class="load-more-history">
        <button 
          class="load-more-btn"
          @click="loadMoreHistory"
          :disabled="loadingHistory"
        >
          <i v-if="loadingHistory" class="icon-loading spinning"></i>
          <span>{{ loadingHistory ? '加载中...' : '加载更多历史消息' }}</span>
        </button>
      </div>

      <!-- 消息列表 -->
      <div class="messages-list">
        <template v-for="(msg, idx) in displayMessages" :key="msg.id || idx">
          <div 
            class="message-wrapper"
            :class="{ 
              'highlighted': searchMode && isMessageHighlighted(msg),
              'user-message': msg.role === 'user',
              'ai-message': msg.role === 'assistant'
            }"
          >
            <!-- 消息时间戳 -->
            <div v-if="shouldShowTimestamp(idx)" class="timestamp-divider">
              {{ formatMessageTime(msg.timestamp) }}
            </div>

            <!-- 消息气泡 -->
            <McBubble
              v-if="msg.role === 'user'"
              :content="highlightSearchText(msg.content)"
              :align="'right'"
              :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/png/demo/userAvatar.svg' }"
              class="message-bubble user-bubble"
            />
            
            <McBubble
              v-else
              :loading="msg.loading ?? false"
              :avatarConfig="{ imgSrc: 'https://matechat.gitcode.com/logo.svg' }"
              class="message-bubble ai-bubble"
            >
              <!-- AI状态显示 -->
              <div v-if="msg.status && msg.status !== 'complete'" class="ai-status">
                <div v-if="msg.status === 'thinking'" class="status-item thinking">
                  <div class="status-icon">
                    <i class="icon-loading spinning"></i>
                  </div>
                  <span class="status-text">正在思考中...</span>
                </div>

                <div v-if="msg.status === 'calling_tools' && msg.toolCalls" class="status-item tools">
                  <div class="status-icon">
                    <i class="icon-tool"></i>
                  </div>
                  <div class="tool-calls">
                    <div class="status-text">正在调用工具:</div>
                    <div v-for="(tool, toolIdx) in msg.toolCalls" :key="toolIdx" class="tool-call">
                      <span class="tool-name">{{ tool.name }}</span>
                      <span class="tool-status" :class="tool.status">
                        <i v-if="tool.status === 'calling'" class="icon-loading spinning"></i>
                        <i v-else-if="tool.status === 'success'" class="icon-check"></i>
                        <i v-else-if="tool.status === 'error'" class="icon-error"></i>
                      </span>
                    </div>
                  </div>
                </div>

                <div v-if="msg.status === 'responding'" class="status-item responding">
                  <div class="status-icon">
                    <i class="icon-edit"></i>
                  </div>
                  <span class="status-text">正在生成回复...</span>
                </div>
              </div>

              <!-- AI消息内容 -->
              <div v-else class="ai-content">
                <div v-html="highlightSearchText(formatAIContent(msg.content))"></div>
                
                <!-- 消息操作 -->
                <div class="message-actions">
                  <button class="action-btn small" @click="copyMessage(msg.content)" title="复制">
                    <i class="icon-copy"></i>
                  </button>
                  <button class="action-btn small" @click="regenerateResponse(msg)" title="重新生成">
                    <i class="icon-refresh"></i>
                  </button>
                  <button class="action-btn small" @click="likeMessage(msg)" title="点赞">
                    <i class="icon-like" :class="{ active: msg.liked }"></i>
                  </button>
                </div>
              </div>
            </McBubble>
          </div>
        </template>
      </div>

      <!-- 滚动到底部按钮 -->
      <div v-if="showScrollToBottom" class="scroll-to-bottom">
        <button class="scroll-btn" @click="scrollToBottom">
          <i class="icon-arrow-down"></i>
          <span>回到底部</span>
        </button>
      </div>
    </div>

    <!-- 输入区域 -->
    <div class="input-area">
      <EnhancedChatInput />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue';
import { useChatStore } from '@/stores/chat';
import EnhancedChatInput from './EnhancedChatInput.vue';

const chatStore = useChatStore();

// 响应式数据
const messagesContainer = ref<HTMLElement>();
const searchMode = ref(false);
const searchQuery = ref('');
const searchResults = ref<any[]>([]);
const isFullscreen = ref(false);
const showScrollToBottom = ref(false);
const hasMoreHistory = ref(true);
const loadingHistory = ref(false);
const lastActivity = ref(new Date());

// 计算属性
const currentSessionTitle = computed(() => {
  return chatStore.currentSession?.title || '新对话';
});

const messageCount = computed(() => {
  return chatStore.messages.length;
});

const displayMessages = computed(() => {
  if (searchMode.value && searchResults.value.length > 0) {
    return searchResults.value;
  }
  return chatStore.messages;
});

// 方法
const toggleSearchMode = () => {
  searchMode.value = !searchMode.value;
  if (!searchMode.value) {
    clearSearch();
  }
};

const onSearchInput = () => {
  if (searchQuery.value.length > 0) {
    performSearch();
  } else {
    clearSearch();
  }
};

const performSearch = () => {
  if (!searchQuery.value.trim()) return;
  
  const query = searchQuery.value.toLowerCase();
  searchResults.value = chatStore.messages.filter(msg => 
    msg.content.toLowerCase().includes(query)
  );
};

const clearSearch = () => {
  searchQuery.value = '';
  searchResults.value = [];
};

const isMessageHighlighted = (msg: any) => {
  return searchResults.value.some(result => result.id === msg.id);
};

const highlightSearchText = (content: string) => {
  if (!searchMode.value || !searchQuery.value.trim()) {
    return content;
  }
  
  const regex = new RegExp(`(${searchQuery.value})`, 'gi');
  return content.replace(regex, '<mark>$1</mark>');
};

const shouldShowTimestamp = (index: number) => {
  if (index === 0) return true;
  
  const currentMsg = displayMessages.value[index];
  const prevMsg = displayMessages.value[index - 1];
  
  if (!currentMsg.timestamp || !prevMsg.timestamp) return false;
  
  const timeDiff = new Date(currentMsg.timestamp).getTime() - new Date(prevMsg.timestamp).getTime();
  return timeDiff > 5 * 60 * 1000; // 5分钟
};

const formatMessageTime = (timestamp: string) => {
  if (!timestamp) return '';
  const date = new Date(timestamp);
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  });
};

const formatLastActivity = (activity: Date) => {
  const now = new Date();
  const diff = now.getTime() - activity.getTime();
  
  if (diff < 60000) return '刚刚';
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`;
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`;
  return `${Math.floor(diff / 86400000)}天前`;
};

const formatAIContent = (content: string) => {
  // 简单的markdown渲染
  return content
    .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
    .replace(/\*(.*?)\*/g, '<em>$1</em>')
    .replace(/`(.*?)`/g, '<code>$1</code>')
    .replace(/\n/g, '<br>');
};

const onScroll = () => {
  if (!messagesContainer.value) return;
  
  const { scrollTop, scrollHeight, clientHeight } = messagesContainer.value;
  showScrollToBottom.value = scrollTop < scrollHeight - clientHeight - 100;
};

const scrollToBottom = () => {
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

const loadMoreHistory = async () => {
  loadingHistory.value = true;
  try {
    // 这里应该调用API加载更多历史消息
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟加载
    hasMoreHistory.value = false; // 假设没有更多历史
  } finally {
    loadingHistory.value = false;
  }
};

const exportSession = () => {
  chatStore.exportSession(chatStore.currentSessionId);
};

const clearSession = () => {
  if (confirm('确定要清空当前会话吗？此操作不可撤销。')) {
    chatStore.clearCurrentSession();
  }
};

const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  document.documentElement.classList.toggle('chat-fullscreen', isFullscreen.value);
};

const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    // 这里可以添加提示
  } catch (err) {
    console.error('复制失败:', err);
  }
};

const regenerateResponse = (msg: any) => {
  // 重新生成响应的逻辑
  console.log('重新生成响应:', msg);
};

const likeMessage = (msg: any) => {
  msg.liked = !msg.liked;
  // 这里可以保存到后端
};

// 生命周期
onMounted(() => {
  nextTick(() => {
    scrollToBottom();
  });
});

// 监听消息变化，自动滚动到底部
watch(() => chatStore.messages.length, () => {
  nextTick(() => {
    if (!showScrollToBottom.value) {
      scrollToBottom();
    }
  });
});
</script>

<style scoped>
/* 样式将在下一个文件中定义 */
</style>
