<template>
  <div class="monitoring-dashboard">
    <!-- 头部 -->
    <div class="dashboard-header">
      <h3 class="dashboard-title">
        <i class="icon-monitor"></i>
        系统监控仪表板
      </h3>
      <div class="header-actions">
        <button @click="refreshData" class="refresh-btn" :disabled="isRefreshing">
          <i class="icon-refresh" :class="{ spinning: isRefreshing }"></i>
          刷新
        </button>
        <button @click="toggleAutoRefresh" class="auto-refresh-btn" :class="{ active: autoRefresh }">
          <i class="icon-auto"></i>
          自动刷新
        </button>
        <button @click="closePanel" class="close-btn">
          <i class="icon-close"></i>
        </button>
      </div>
    </div>

    <!-- 系统健康状态 -->
    <div class="health-overview">
      <div class="health-card" :class="systemHealthClass">
        <div class="health-icon">
          <i :class="systemHealthIcon"></i>
        </div>
        <div class="health-info">
          <h4>系统状态</h4>
          <span class="health-status">{{ systemHealthText }}</span>
          <span class="health-time">最后更新: {{ lastUpdateTime }}</span>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <i class="icon-sessions"></i>
        </div>
        <div class="stat-content">
          <h4>活跃会话</h4>
          <div class="stat-number">{{ activeSessionsCount }}</div>
          <div class="stat-change" :class="getChangeClass(activeSessionsChange)">
            <i :class="getChangeIcon(activeSessionsChange)"></i>
            {{ Math.abs(activeSessionsChange) }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="icon-users"></i>
        </div>
        <div class="stat-content">
          <h4>活跃用户</h4>
          <div class="stat-number">{{ activeUsersCount }}</div>
          <div class="stat-change" :class="getChangeClass(activeUsersChange)">
            <i :class="getChangeIcon(activeUsersChange)"></i>
            {{ Math.abs(activeUsersChange) }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="icon-messages"></i>
        </div>
        <div class="stat-content">
          <h4>消息总数</h4>
          <div class="stat-number">{{ totalMessages }}</div>
          <div class="stat-change" :class="getChangeClass(messagesChange)">
            <i :class="getChangeIcon(messagesChange)"></i>
            {{ Math.abs(messagesChange) }}
          </div>
        </div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <i class="icon-activity"></i>
        </div>
        <div class="stat-content">
          <h4>活跃率</h4>
          <div class="stat-number">{{ activityRate }}%</div>
          <div class="stat-change" :class="getChangeClass(activityRateChange)">
            <i :class="getChangeIcon(activityRateChange)"></i>
            {{ Math.abs(activityRateChange) }}%
          </div>
        </div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <!-- 会话持续时间分析 -->
      <div class="chart-card">
        <div class="chart-header">
          <h4>会话持续时间分析</h4>
          <select v-model="durationTimeRange" @change="updateDurationChart" class="time-range-select">
            <option value="24">最近24小时</option>
            <option value="168">最近7天</option>
            <option value="720">最近30天</option>
          </select>
        </div>
        <div class="chart-content">
          <div v-if="durationData" class="duration-stats">
            <div class="duration-item">
              <span class="duration-label">平均时长</span>
              <span class="duration-value">{{ formatDuration(durationData.average_duration) }}</span>
            </div>
            <div class="duration-item">
              <span class="duration-label">最长时长</span>
              <span class="duration-value">{{ formatDuration(durationData.max_duration) }}</span>
            </div>
            <div class="duration-item">
              <span class="duration-label">分析会话</span>
              <span class="duration-value">{{ durationData.total_sessions }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 消息频率统计 -->
      <div class="chart-card">
        <div class="chart-header">
          <h4>消息频率统计</h4>
          <select v-model="frequencyTimeRange" @change="updateFrequencyChart" class="time-range-select">
            <option value="1">最近1天</option>
            <option value="7">最近7天</option>
            <option value="30">最近30天</option>
          </select>
        </div>
        <div class="chart-content">
          <div v-if="frequencyData" class="frequency-stats">
            <div class="frequency-item">
              <span class="frequency-label">总消息数</span>
              <span class="frequency-value">{{ frequencyData.total_messages }}</span>
            </div>
            <div class="frequency-item">
              <span class="frequency-label">平均每天</span>
              <span class="frequency-value">{{ Math.round(frequencyData.average_per_day) }}</span>
            </div>
            <div class="frequency-item">
              <span class="frequency-label">峰值时段</span>
              <span class="frequency-value">{{ frequencyData.peak_hour || 'N/A' }}:00</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 用户活跃度分析 -->
    <div class="activity-section">
      <div class="activity-header">
        <h4>用户活跃度分析</h4>
        <div class="activity-controls">
          <select v-model="activityLimit" @change="updateActivityData" class="limit-select">
            <option value="10">前10名</option>
            <option value="20">前20名</option>
            <option value="50">前50名</option>
          </select>
        </div>
      </div>
      
      <div class="activity-content">
        <div v-if="activityData && activityData.top_users" class="user-list">
          <div 
            v-for="(user, index) in activityData.top_users" 
            :key="user.user_id"
            class="user-item"
          >
            <div class="user-rank">{{ index + 1 }}</div>
            <div class="user-info">
              <div class="user-id">{{ user.user_id }}</div>
              <div class="user-stats">
                {{ user.session_count }} 会话 · {{ user.message_count }} 消息
              </div>
            </div>
            <div class="user-activity">
              <div class="activity-bar">
                <div 
                  class="activity-fill" 
                  :style="{ width: (user.session_count / activityData.max_sessions * 100) + '%' }"
                ></div>
              </div>
              <span class="activity-score">{{ user.activity_score }}%</span>
            </div>
          </div>
        </div>
        
        <div v-else class="no-activity-data">
          <i class="icon-no-data"></i>
          <span>暂无用户活跃度数据</span>
        </div>
      </div>
    </div>

    <!-- 系统资源使用情况 -->
    <div class="resources-section">
      <h4>系统资源</h4>
      <div class="resources-grid">
        <div class="resource-item">
          <div class="resource-label">数据库大小</div>
          <div class="resource-value">{{ formatFileSize(systemHealth?.database_size || 0) }}</div>
        </div>
        <div class="resource-item">
          <div class="resource-label">总会话数</div>
          <div class="resource-value">{{ systemHealth?.total_sessions || 0 }}</div>
        </div>
        <div class="resource-item">
          <div class="resource-label">总用户数</div>
          <div class="resource-value">{{ systemHealth?.total_users || 0 }}</div>
        </div>
        <div class="resource-item">
          <div class="resource-label">运行时间</div>
          <div class="resource-value">{{ formatUptime(systemHealth?.uptime || 0) }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '@/stores/chat';
import type { MonitoringDashboardData, SystemHealthMetrics } from '@/services/monitoring';

const chatStore = useChatStore();

// 响应式数据
const isRefreshing = ref(false);
const autoRefresh = ref(true);
const refreshInterval = ref<number | null>(null);
const durationTimeRange = ref('24');
const frequencyTimeRange = ref('7');
const activityLimit = ref('20');

// 历史数据用于计算变化
const previousData = ref<MonitoringDashboardData | null>(null);

// 计算属性
const monitoringData = computed(() => chatStore.monitoringData);
const systemHealth = computed(() => chatStore.systemHealth);

const activeSessionsCount = computed(() => 
  monitoringData.value?.activeSessionsStats?.active_sessions || 0
);

const activeUsersCount = computed(() => 
  monitoringData.value?.activeSessionsStats?.active_users || 0
);

const totalMessages = computed(() => 
  monitoringData.value?.messageFrequencyStats?.total_messages || 0
);

const activityRate = computed(() => 
  Math.round(monitoringData.value?.activeSessionsStats?.activity_rate || 0)
);

const durationData = computed(() => 
  monitoringData.value?.sessionDurationAnalysis
);

const frequencyData = computed(() => 
  monitoringData.value?.messageFrequencyStats
);

const activityData = computed(() => 
  monitoringData.value?.userActivityAnalysis
);

const lastUpdateTime = computed(() => {
  if (!monitoringData.value?.lastUpdated) return 'N/A';
  return new Date(monitoringData.value.lastUpdated).toLocaleTimeString('zh-CN');
});

const systemHealthClass = computed(() => {
  const status = systemHealth.value?.status;
  return {
    'health-good': status === 'healthy',
    'health-warning': status === 'warning',
    'health-error': status === 'error'
  };
});

const systemHealthIcon = computed(() => {
  const status = systemHealth.value?.status;
  switch (status) {
    case 'healthy': return 'icon-check-circle';
    case 'warning': return 'icon-warning-circle';
    case 'error': return 'icon-error-circle';
    default: return 'icon-question-circle';
  }
});

const systemHealthText = computed(() => {
  const status = systemHealth.value?.status;
  switch (status) {
    case 'healthy': return '运行正常';
    case 'warning': return '需要注意';
    case 'error': return '存在问题';
    default: return '状态未知';
  }
});

// 计算数据变化
const activeSessionsChange = computed(() => {
  if (!previousData.value) return 0;
  const current = activeSessionsCount.value;
  const previous = previousData.value.activeSessionsStats?.active_sessions || 0;
  return current - previous;
});

const activeUsersChange = computed(() => {
  if (!previousData.value) return 0;
  const current = activeUsersCount.value;
  const previous = previousData.value.activeSessionsStats?.active_users || 0;
  return current - previous;
});

const messagesChange = computed(() => {
  if (!previousData.value) return 0;
  const current = totalMessages.value;
  const previous = previousData.value.messageFrequencyStats?.total_messages || 0;
  return current - previous;
});

const activityRateChange = computed(() => {
  if (!previousData.value) return 0;
  const current = activityRate.value;
  const previous = Math.round(previousData.value.activeSessionsStats?.activity_rate || 0);
  return current - previous;
});

// 方法
const closePanel = () => {
  chatStore.toggleMonitoringDashboard();
};

const refreshData = async () => {
  isRefreshing.value = true;
  try {
    // 保存当前数据作为历史数据
    if (monitoringData.value) {
      previousData.value = { ...monitoringData.value };
    }
    
    await chatStore.refreshMonitoringData();
  } finally {
    isRefreshing.value = false;
  }
};

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value;
  if (autoRefresh.value) {
    startAutoRefresh();
  } else {
    stopAutoRefresh();
  }
};

const startAutoRefresh = () => {
  if (refreshInterval.value) return;
  
  refreshInterval.value = window.setInterval(() => {
    refreshData();
  }, 30000); // 30秒刷新一次
};

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value);
    refreshInterval.value = null;
  }
};

const updateDurationChart = () => {
  // TODO: 根据时间范围更新图表
  console.log('更新持续时间图表:', durationTimeRange.value);
};

const updateFrequencyChart = () => {
  // TODO: 根据时间范围更新图表
  console.log('更新频率图表:', frequencyTimeRange.value);
};

const updateActivityData = () => {
  // TODO: 根据限制更新活跃度数据
  console.log('更新活跃度数据:', activityLimit.value);
};

const getChangeClass = (change: number): string => {
  if (change > 0) return 'positive';
  if (change < 0) return 'negative';
  return 'neutral';
};

const getChangeIcon = (change: number): string => {
  if (change > 0) return 'icon-arrow-up';
  if (change < 0) return 'icon-arrow-down';
  return 'icon-minus';
};

const formatDuration = (seconds: number): string => {
  if (seconds < 60) return `${seconds}秒`;
  if (seconds < 3600) return `${Math.round(seconds / 60)}分钟`;
  return `${Math.round(seconds / 3600)}小时`;
};

const formatFileSize = (bytes: number): string => {
  if (bytes < 1024) return `${bytes} B`;
  if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
  return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
};

const formatUptime = (seconds: number): string => {
  const days = Math.floor(seconds / (24 * 3600));
  const hours = Math.floor((seconds % (24 * 3600)) / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  
  if (days > 0) return `${days}天${hours}小时`;
  if (hours > 0) return `${hours}小时${minutes}分钟`;
  return `${minutes}分钟`;
};

// 生命周期
onMounted(() => {
  refreshData();
  if (autoRefresh.value) {
    startAutoRefresh();
  }
});

onUnmounted(() => {
  stopAutoRefresh();
});
</script>
