<template>
  <div class="enhanced-session-manager">
    <!-- 头部工具栏 -->
    <div class="session-toolbar">
      <div class="toolbar-left">
        <h3 class="toolbar-title">会话管理</h3>
        <span class="session-count">{{ filteredSessions.length }} 个会话</span>
      </div>
      <div class="toolbar-right">
        <button @click="toggleRecoveryPanel" class="toolbar-btn recovery-btn" title="恢复会话">
          <i class="icon-recovery"></i>
          恢复
        </button>
        <button @click="toggleMonitoringDashboard" class="toolbar-btn monitoring-btn" title="监控面板">
          <i class="icon-monitor"></i>
          监控
        </button>
        <button @click="createNewSession" class="toolbar-btn primary-btn" title="新建会话">
          <i class="icon-plus"></i>
          新建
        </button>
      </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="session-filters">
      <div class="search-box">
        <input 
          v-model="searchKey" 
          placeholder="搜索会话标题..." 
          class="search-input"
          @input="onSearch"
        />
        <i class="icon-search"></i>
      </div>
      
      <div class="filter-options">
        <select v-model="sortBy" @change="onSortChange" class="sort-select">
          <option value="lastActive">最近活跃</option>
          <option value="created">创建时间</option>
          <option value="messageCount">消息数量</option>
          <option value="title">标题</option>
        </select>
        
        <select v-model="filterBy" @change="onFilterChange" class="filter-select">
          <option value="all">全部会话</option>
          <option value="active">活跃会话</option>
          <option value="recent">最近7天</option>
          <option value="hasMessages">有消息</option>
        </select>
      </div>
    </div>

    <!-- 会话列表 -->
    <div class="session-list-container">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>加载会话中...</span>
      </div>
      
      <div v-else-if="filteredSessions.length === 0" class="empty-state">
        <div class="empty-icon">📝</div>
        <h4>暂无会话</h4>
        <p>{{ searchKey ? '没有找到匹配的会话' : '开始您的第一个对话吧' }}</p>
        <button v-if="!searchKey" @click="createNewSession" class="create-first-btn">
          创建会话
        </button>
      </div>
      
      <div v-else class="session-list">
        <div 
          v-for="session in paginatedSessions" 
          :key="session.id"
          class="session-item"
          :class="{ 
            active: session.id === chatStore.currentSessionId,
            'has-messages': session.messageCount > 0
          }"
          @click="selectSession(session.id)"
        >
          <!-- 会话信息 -->
          <div class="session-info">
            <div class="session-header">
              <h4 class="session-title">{{ session.title || '新对话' }}</h4>
              <div class="session-status">
                <span v-if="session.messageCount > 0" class="message-badge">
                  {{ session.messageCount }}
                </span>
                <span class="activity-indicator" :class="getActivityLevel(session)"></span>
              </div>
            </div>
            
            <div class="session-meta">
              <span class="created-time">
                创建于 {{ formatDate(session.createdAt) }}
              </span>
              <span class="last-active">
                最后活跃 {{ formatRelativeTime(session.lastActiveTime) }}
              </span>
            </div>
            
            <div v-if="session.messageCount > 0" class="session-preview">
              {{ getSessionPreview(session) }}
            </div>
          </div>

          <!-- 会话操作 -->
          <div class="session-actions" @click.stop>
            <button 
              @click="startEdit(session.id, session.title)" 
              class="action-btn edit-btn" 
              title="重命名"
            >
              <i class="icon-edit"></i>
            </button>
            
            <button 
              @click="duplicateSession(session.id)" 
              class="action-btn duplicate-btn" 
              title="复制会话"
            >
              <i class="icon-copy"></i>
            </button>
            
            <button 
              @click="exportSession(session.id)" 
              class="action-btn export-btn" 
              title="导出会话"
            >
              <i class="icon-download"></i>
            </button>
            
            <button 
              @click="deleteSession(session.id)" 
              class="action-btn delete-btn" 
              title="删除会话"
            >
              <i class="icon-delete"></i>
            </button>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="pagination">
        <button 
          @click="currentPage--" 
          :disabled="currentPage === 1"
          class="page-btn"
        >
          上一页
        </button>
        
        <span class="page-info">
          第 {{ currentPage }} 页，共 {{ totalPages }} 页
        </span>
        
        <button 
          @click="currentPage++" 
          :disabled="currentPage === totalPages"
          class="page-btn"
        >
          下一页
        </button>
      </div>
    </div>

    <!-- 批量操作 -->
    <div v-if="selectedSessions.length > 0" class="batch-actions">
      <div class="batch-info">
        已选择 {{ selectedSessions.length }} 个会话
      </div>
      <div class="batch-buttons">
        <button @click="batchDelete" class="batch-btn delete-btn">
          批量删除
        </button>
        <button @click="batchExport" class="batch-btn export-btn">
          批量导出
        </button>
        <button @click="clearSelection" class="batch-btn cancel-btn">
          取消选择
        </button>
      </div>
    </div>

    <!-- 编辑对话框 -->
    <div v-if="editingSessionId" class="edit-modal" @click="cancelEdit">
      <div class="edit-dialog" @click.stop>
        <h4>重命名会话</h4>
        <input 
          v-model="editingTitle"
          @keyup.enter="saveSessionTitle"
          @keyup.escape="cancelEdit"
          class="edit-input"
          ref="editInput"
          placeholder="输入新的会话标题"
        />
        <div class="edit-actions">
          <button @click="saveSessionTitle" class="save-btn">保存</button>
          <button @click="cancelEdit" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, nextTick, onMounted, watch } from 'vue';
import { useChatStore } from '@/stores/chat';
import type { Session } from '@/stores/chat';

const chatStore = useChatStore();

// 响应式数据
const searchKey = ref('');
const sortBy = ref('lastActive');
const filterBy = ref('all');
const currentPage = ref(1);
const pageSize = ref(20);
const isLoading = ref(false);
const selectedSessions = ref<string[]>([]);

// 编辑相关
const editingSessionId = ref<string | null>(null);
const editingTitle = ref('');
const editInput = ref<HTMLInputElement>();

// 计算属性
const filteredSessions = computed(() => {
  let sessions = [...chatStore.sessions];
  
  // 搜索过滤
  if (searchKey.value) {
    const keyword = searchKey.value.toLowerCase();
    sessions = sessions.filter(session => 
      session.title.toLowerCase().includes(keyword)
    );
  }
  
  // 类型过滤
  switch (filterBy.value) {
    case 'active':
      sessions = sessions.filter(session => 
        new Date().getTime() - session.lastActiveTime.getTime() < 24 * 60 * 60 * 1000
      );
      break;
    case 'recent':
      sessions = sessions.filter(session => 
        new Date().getTime() - session.createdAt.getTime() < 7 * 24 * 60 * 60 * 1000
      );
      break;
    case 'hasMessages':
      sessions = sessions.filter(session => session.messageCount > 0);
      break;
  }
  
  // 排序
  sessions.sort((a, b) => {
    switch (sortBy.value) {
      case 'created':
        return b.createdAt.getTime() - a.createdAt.getTime();
      case 'messageCount':
        return b.messageCount - a.messageCount;
      case 'title':
        return a.title.localeCompare(b.title);
      case 'lastActive':
      default:
        return b.lastActiveTime.getTime() - a.lastActiveTime.getTime();
    }
  });
  
  return sessions;
});

const totalPages = computed(() => 
  Math.ceil(filteredSessions.value.length / pageSize.value)
);

const paginatedSessions = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredSessions.value.slice(start, end);
});

// 方法
const createNewSession = () => {
  chatStore.createSession();
};

const selectSession = (sessionId: string) => {
  chatStore.selectSession(sessionId);
};

const deleteSession = (sessionId: string) => {
  if (confirm('确定要删除这个会话吗？删除后可以在回收站中恢复。')) {
    chatStore.deleteSession(sessionId);
  }
};

const startEdit = async (sessionId: string, currentTitle: string) => {
  editingSessionId.value = sessionId;
  editingTitle.value = currentTitle || '新对话';
  
  await nextTick();
  if (editInput.value) {
    editInput.value.focus();
    editInput.value.select();
  }
};

const saveSessionTitle = () => {
  if (editingSessionId.value && editingTitle.value.trim()) {
    chatStore.renameSession(editingSessionId.value, editingTitle.value.trim());
  }
  cancelEdit();
};

const cancelEdit = () => {
  editingSessionId.value = null;
  editingTitle.value = '';
};

const duplicateSession = (sessionId: string) => {
  // TODO: 实现会话复制功能
  console.log('复制会话:', sessionId);
};

const exportSession = (sessionId: string) => {
  // TODO: 实现会话导出功能
  console.log('导出会话:', sessionId);
};

const batchDelete = () => {
  if (confirm(`确定要删除选中的 ${selectedSessions.value.length} 个会话吗？`)) {
    selectedSessions.value.forEach(sessionId => {
      chatStore.deleteSession(sessionId);
    });
    clearSelection();
  }
};

const batchExport = () => {
  // TODO: 实现批量导出功能
  console.log('批量导出:', selectedSessions.value);
};

const clearSelection = () => {
  selectedSessions.value = [];
};

const toggleRecoveryPanel = () => {
  chatStore.toggleRecoveryPanel();
};

const toggleMonitoringDashboard = () => {
  chatStore.toggleMonitoringDashboard();
};

const onSearch = () => {
  currentPage.value = 1; // 重置到第一页
};

const onSortChange = () => {
  currentPage.value = 1;
};

const onFilterChange = () => {
  currentPage.value = 1;
};

const getActivityLevel = (session: Session): string => {
  const hoursSinceActive = (new Date().getTime() - session.lastActiveTime.getTime()) / (1000 * 60 * 60);
  if (hoursSinceActive < 1) return 'very-active';
  if (hoursSinceActive < 24) return 'active';
  if (hoursSinceActive < 168) return 'moderate';
  return 'inactive';
};

const getSessionPreview = (session: Session): string => {
  // TODO: 从会话消息中获取预览文本
  return `${session.messageCount} 条消息的对话`;
};

const formatDate = (date: Date): string => {
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  });
};

const formatRelativeTime = (date: Date): string => {
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const minutes = Math.floor(diff / (1000 * 60));
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (minutes < 1) return '刚刚';
  if (minutes < 60) return `${minutes} 分钟前`;
  if (hours < 24) return `${hours} 小时前`;
  if (days < 7) return `${days} 天前`;
  return formatDate(date);
};

// 生命周期
onMounted(() => {
  // 初始化会话列表
  chatStore.initializeStore();
});

// 监听搜索关键词变化
watch(searchKey, () => {
  currentPage.value = 1;
});
</script>
