<template>
  <div class="chat-view-wrapper">
    <div class="chat-view-container">
      <!-- 移动端遮罩层 -->
      <div
        v-if="!sidebarCollapsed && isMobile"
        class="sidebar-overlay"
        @click="toggleSidebar"
      ></div>

      <!-- 左侧会话历史面板 - 可折叠 -->
      <div class="sidebar-container" :class="{ 'collapsed': sidebarCollapsed }">
        <div class="sidebar-header">
          <div class="sidebar-title">
            <img src="https://matechat.gitcode.com/logo.svg" alt="MateChat" class="logo" />
            <span v-if="!sidebarCollapsed" class="title-text">{{ getSidebarTitle() }}</span>
          </div>
          <button
            class="collapse-btn"
            @click="toggleSidebar"
            :title="sidebarCollapsed ? '展开侧边栏' : '折叠侧边栏'"
          >
            <span v-if="sidebarCollapsed">→</span>
            <span v-else">←</span>
          </button>
        </div>

        <div class="sidebar-content" v-if="!sidebarCollapsed">
          <!-- 标签页导航 -->
          <div class="sidebar-tabs">
            <button
              v-for="tab in sidebarTabs"
              :key="tab.key"
              class="tab-btn"
              :class="{ 'active': currentSidebarTab === tab.key }"
              @click="currentSidebarTab = tab.key"
              :title="tab.title"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-text">{{ tab.label }}</span>
            </button>
          </div>

          <!-- 标签页内容 -->
          <div class="sidebar-tab-content">
            <HistoryList v-if="currentSidebarTab === 'history'" />
            <EnhancedSessionManager v-else-if="currentSidebarTab === 'sessions'" />
            <SessionRecoveryPanel v-else-if="currentSidebarTab === 'recovery'" />
            <MonitoringDashboard v-else-if="currentSidebarTab === 'monitoring'" />
          </div>
        </div>
      </div>

      <!-- 主聊天区域 -->
      <div class="main-chat-area">
        <!-- 增强聊天界面 -->
        <EnhancedChatInterface v-if="chatStore.startChat" />
        <Welcome v-else />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue';
import { useChatStore } from '@/stores/chat';
import HistoryList from './HistoryList.vue';
import EnhancedSessionManager from './EnhancedSessionManager.vue';
import SessionRecoveryPanel from './SessionRecoveryPanel.vue';
import MonitoringDashboard from './MonitoringDashboard.vue';
import NavbarTop from './NavbarTop.vue';
import EnhancedChatInterface from './EnhancedChatInterface.vue';
import Welcome from './Welcome.vue';

const chatStore = useChatStore();

// 控制侧边栏折叠状态
const sidebarCollapsed = ref(false);

// 检测是否为移动端
const isMobile = ref(false);

// 当前侧边栏标签页
const currentSidebarTab = ref('history');

// 侧边栏标签页配置
const sidebarTabs = [
  { key: 'history', label: '对话历史', icon: '💬', title: '查看对话历史' },
  { key: 'sessions', label: '会话管理', icon: '📋', title: '管理所有会话' },
  { key: 'recovery', label: '恢复面板', icon: '🔄', title: '恢复已删除的会话' },
  { key: 'monitoring', label: '监控面板', icon: '📊', title: '查看系统监控数据' }
];

// 获取侧边栏标题
const getSidebarTitle = () => {
  const tab = sidebarTabs.find(t => t.key === currentSidebarTab.value);
  return tab ? tab.label : '对话历史';
};

// 检测屏幕尺寸
const checkScreenSize = () => {
  isMobile.value = window.innerWidth <= 768;
  // 移动端默认折叠侧边栏
  if (isMobile.value && !localStorage.getItem('sidebarCollapsed')) {
    sidebarCollapsed.value = true;
  }
};

// 切换侧边栏状态
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value;
  // 保存状态到localStorage
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString());
};

// 从localStorage恢复侧边栏状态
const restoreSidebarState = () => {
  const saved = localStorage.getItem('sidebarCollapsed');
  if (saved !== null) {
    sidebarCollapsed.value = saved === 'true';
  }
};

// 组件挂载时初始化
onMounted(() => {
  // 检测屏幕尺寸
  checkScreenSize();

  // 恢复侧边栏状态
  restoreSidebarState();

  // 监听窗口大小变化
  window.addEventListener('resize', checkScreenSize);

  // 初始化store（加载会话数据）
  chatStore.initializeStore();

  // 建立WebSocket连接以检测连接状态
  chatStore.connectWebSocket();
});

// 组件卸载时清理连接
onUnmounted(() => {
  chatStore.disconnectWebSocket();
  window.removeEventListener('resize', checkScreenSize);
});
</script>

<style scoped lang="scss">
.chat-view-wrapper {
  position: relative;
  display: flex;
  width: 100%;
  height: 100vh;
  background: var(--devui-global-bg, #f8f9fa);
  overflow: hidden;
}

.chat-view-container {
  display: flex;
  width: 100%;
  height: 100%;
  // 确保填满整个浏览器窗口，无空白区域
}

// 左侧边栏样式
.sidebar-container {
  flex-shrink: 0; // 防止被压缩
  width: 280px;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-right: 1px solid var(--devui-dividing-line, #ddd);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;

  &.collapsed {
    width: 60px;
  }

  .sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);

    .sidebar-title {
      display: flex;
      align-items: center;
      gap: 8px;
      color: white;
      font-weight: 600;

      .logo {
        width: 24px;
        height: 24px;
        filter: brightness(0) invert(1); // 让logo变白色
      }

      .title-text {
        font-size: 16px;
        white-space: nowrap;
      }
    }

    .collapse-btn {
      background: rgba(255, 255, 255, 0.1);
      border: none;
      color: white;
      width: 28px;
      height: 28px;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }

  .sidebar-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
  }

  .sidebar-tabs {
    display: flex;
    flex-direction: column;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding: 8px;
    gap: 4px;

    .tab-btn {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 12px;
      background: transparent;
      border: none;
      color: rgba(255, 255, 255, 0.8);
      cursor: pointer;
      border-radius: 6px;
      transition: all 0.2s;
      font-size: 13px;
      text-align: left;

      &:hover {
        background: rgba(255, 255, 255, 0.1);
        color: white;
      }

      &.active {
        background: rgba(255, 255, 255, 0.15);
        color: white;
        font-weight: 500;
      }

      .tab-icon {
        font-size: 14px;
        flex-shrink: 0;
      }

      .tab-text {
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .sidebar-tab-content {
    flex: 1;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px 8px 0 0;
    margin: 8px;
    margin-bottom: 0;
  }
}

// 主聊天区域样式
.main-chat-area {
  flex: 1; // 占据所有剩余空间
  display: flex;
  flex-direction: column;
  height: 100%;
  min-width: 0; // 允许收缩
  background: var(--devui-base-bg, white);
  overflow: hidden; // 防止内容溢出
}

// 响应式设计
@media (max-width: 1024px) {
  .sidebar-container {
    width: 240px;

    &.collapsed {
      width: 50px;
    }
  }
}

@media (max-width: 768px) {
  .chat-view-container {
    position: relative;
  }

  .sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 999;
    backdrop-filter: blur(2px);
  }

  .sidebar-container {
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1000;
    box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
    transform: translateX(0);
    transition: transform 0.3s ease;

    &.collapsed {
      transform: translateX(-100%);
    }
  }

  .main-chat-area {
    width: 100%;
    margin-left: 0;
  }
}

// 超大屏幕优化
@media (min-width: 1920px) {
  .sidebar-container {
    width: 320px;

    &.collapsed {
      width: 70px;
    }
  }
}
</style>
