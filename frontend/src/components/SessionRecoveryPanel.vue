<template>
  <div class="session-recovery-panel">
    <!-- 头部 -->
    <div class="recovery-header">
      <h3 class="recovery-title">
        <i class="icon-recovery"></i>
        会话恢复
      </h3>
      <button @click="closePanel" class="close-btn">
        <i class="icon-close"></i>
      </button>
    </div>

    <!-- 恢复统计 -->
    <div class="recovery-stats">
      <div class="stat-item">
        <span class="stat-number">{{ recoverableSessions.length }}</span>
        <span class="stat-label">可恢复会话</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ completedRecoveries }}</span>
        <span class="stat-label">已恢复</span>
      </div>
      <div class="stat-item">
        <span class="stat-number">{{ failedRecoveries }}</span>
        <span class="stat-label">恢复失败</span>
      </div>
    </div>

    <!-- 操作工具栏 -->
    <div class="recovery-toolbar">
      <button @click="refreshRecoverableSessions" class="toolbar-btn refresh-btn" :disabled="isLoading">
        <i class="icon-refresh" :class="{ spinning: isLoading }"></i>
        刷新列表
      </button>
      
      <button 
        @click="batchRecover" 
        class="toolbar-btn batch-btn" 
        :disabled="selectedSessions.length === 0 || isRecovering"
      >
        <i class="icon-batch"></i>
        批量恢复 ({{ selectedSessions.length }})
      </button>
      
      <button @click="clearFailedRecoveries" class="toolbar-btn clear-btn">
        <i class="icon-clear"></i>
        清除失败记录
      </button>
    </div>

    <!-- 会话列表 -->
    <div class="recovery-list-container">
      <div v-if="isLoading" class="loading-state">
        <div class="loading-spinner"></div>
        <span>加载可恢复会话中...</span>
      </div>
      
      <div v-else-if="recoverableSessions.length === 0" class="empty-state">
        <div class="empty-icon">🔄</div>
        <h4>暂无可恢复的会话</h4>
        <p>所有会话都处于正常状态</p>
      </div>
      
      <div v-else class="recovery-list">
        <div class="list-header">
          <label class="select-all">
            <input 
              type="checkbox" 
              :checked="isAllSelected"
              @change="toggleSelectAll"
            />
            全选
          </label>
          <span class="session-count">{{ recoverableSessions.length }} 个会话</span>
        </div>

        <div 
          v-for="session in recoverableSessions" 
          :key="session.session_id"
          class="recovery-item"
          :class="{ 
            selected: selectedSessions.includes(session.session_id),
            recovering: isSessionRecovering(session.session_id),
            recovered: recoveryResults[session.session_id]?.success,
            failed: recoveryResults[session.session_id]?.success === false
          }"
        >
          <!-- 选择框 -->
          <label class="session-checkbox">
            <input 
              type="checkbox" 
              :value="session.session_id"
              v-model="selectedSessions"
              :disabled="isSessionRecovering(session.session_id)"
            />
          </label>

          <!-- 会话信息 -->
          <div class="session-info">
            <div class="session-header">
              <h4 class="session-title">{{ session.title || '未命名会话' }}</h4>
              <div class="session-badges">
                <span class="integrity-badge" :class="getIntegrityClass(session)">
                  {{ getIntegrityText(session) }}
                </span>
                <span v-if="session.can_recover" class="recoverable-badge">
                  可恢复
                </span>
              </div>
            </div>
            
            <div class="session-meta">
              <span class="created-time">
                创建于 {{ formatDate(session.created_at) }}
              </span>
              <span class="message-count">
                {{ session.message_count || 0 }} 条消息
              </span>
              <span v-if="session.last_activity" class="last-activity">
                最后活跃 {{ formatRelativeTime(session.last_activity) }}
              </span>
            </div>

            <!-- 恢复进度 -->
            <div v-if="getRecoveryProgress(session.session_id)" class="recovery-progress">
              <div class="progress-bar">
                <div 
                  class="progress-fill" 
                  :style="{ width: getRecoveryProgress(session.session_id).progress + '%' }"
                ></div>
              </div>
              <span class="progress-text">
                {{ getRecoveryProgress(session.session_id).message }}
              </span>
            </div>

            <!-- 恢复结果 -->
            <div v-if="recoveryResults[session.session_id]" class="recovery-result">
              <div v-if="recoveryResults[session.session_id].success" class="success-result">
                <i class="icon-success"></i>
                恢复成功 - {{ formatTime(recoveryResults[session.session_id].timestamp) }}
              </div>
              <div v-else class="error-result">
                <i class="icon-error"></i>
                恢复失败: {{ recoveryResults[session.session_id].error }}
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="session-actions">
            <button 
              @click="recoverSingleSession(session.session_id)"
              class="action-btn recover-btn"
              :disabled="isSessionRecovering(session.session_id) || recoveryResults[session.session_id]?.success"
              title="恢复此会话"
            >
              <i class="icon-recover"></i>
              {{ isSessionRecovering(session.session_id) ? '恢复中...' : '恢复' }}
            </button>
            
            <button 
              @click="checkIntegrity(session.session_id)"
              class="action-btn check-btn"
              title="检查完整性"
            >
              <i class="icon-check"></i>
              检查
            </button>
            
            <button 
              @click="viewSessionDetails(session)"
              class="action-btn details-btn"
              title="查看详情"
            >
              <i class="icon-details"></i>
              详情
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 详情对话框 -->
    <div v-if="selectedSessionDetails" class="details-modal" @click="closeDetails">
      <div class="details-dialog" @click.stop>
        <div class="details-header">
          <h4>会话详情</h4>
          <button @click="closeDetails" class="close-btn">
            <i class="icon-close"></i>
          </button>
        </div>
        
        <div class="details-content">
          <div class="detail-section">
            <h5>基本信息</h5>
            <div class="detail-grid">
              <div class="detail-item">
                <label>会话ID:</label>
                <span>{{ selectedSessionDetails.session_id }}</span>
              </div>
              <div class="detail-item">
                <label>标题:</label>
                <span>{{ selectedSessionDetails.title || '未命名' }}</span>
              </div>
              <div class="detail-item">
                <label>创建时间:</label>
                <span>{{ formatDateTime(selectedSessionDetails.created_at) }}</span>
              </div>
              <div class="detail-item">
                <label>消息数量:</label>
                <span>{{ selectedSessionDetails.message_count || 0 }}</span>
              </div>
            </div>
          </div>

          <div class="detail-section">
            <h5>完整性状态</h5>
            <div class="integrity-details">
              <div class="integrity-item" :class="{ valid: selectedSessionDetails.metadata_exists }">
                <i :class="selectedSessionDetails.metadata_exists ? 'icon-check' : 'icon-error'"></i>
                元数据存在
              </div>
              <div class="integrity-item" :class="{ valid: selectedSessionDetails.state_consistent }">
                <i :class="selectedSessionDetails.state_consistent ? 'icon-check' : 'icon-error'"></i>
                状态一致
              </div>
              <div class="integrity-item" :class="{ valid: selectedSessionDetails.can_recover }">
                <i :class="selectedSessionDetails.can_recover ? 'icon-check' : 'icon-error'"></i>
                可以恢复
              </div>
            </div>
          </div>

          <div v-if="selectedSessionDetails.error" class="detail-section">
            <h5>错误信息</h5>
            <div class="error-message">
              {{ selectedSessionDetails.error }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { useChatStore } from '@/stores/chat';
import type { RecoverableSession, RecoveryProgress } from '@/services/recovery';

const chatStore = useChatStore();

// 响应式数据
const isLoading = ref(false);
const isRecovering = ref(false);
const selectedSessions = ref<string[]>([]);
const recoveryResults = ref<Record<string, { success: boolean; error?: string; timestamp: Date }>>({});
const selectedSessionDetails = ref<RecoverableSession | null>(null);

// 计算属性
const recoverableSessions = computed(() => chatStore.recoverableSessions);
const recoveryProgress = computed(() => chatStore.recoveryProgress);

const isAllSelected = computed(() => 
  recoverableSessions.value.length > 0 && 
  selectedSessions.value.length === recoverableSessions.value.length
);

const completedRecoveries = computed(() => 
  Object.values(recoveryResults.value).filter(result => result.success).length
);

const failedRecoveries = computed(() => 
  Object.values(recoveryResults.value).filter(result => !result.success).length
);

// 方法
const closePanel = () => {
  chatStore.toggleRecoveryPanel();
};

const refreshRecoverableSessions = async () => {
  isLoading.value = true;
  try {
    await chatStore.loadRecoverableSessions();
  } finally {
    isLoading.value = false;
  }
};

const toggleSelectAll = () => {
  if (isAllSelected.value) {
    selectedSessions.value = [];
  } else {
    selectedSessions.value = recoverableSessions.value.map(s => s.session_id);
  }
};

const isSessionRecovering = (sessionId: string): boolean => {
  const progress = recoveryProgress.value.get(sessionId);
  return progress?.status === 'preparing' || progress?.status === 'restoring';
};

const getRecoveryProgress = (sessionId: string): RecoveryProgress | null => {
  return recoveryProgress.value.get(sessionId) || null;
};

const recoverSingleSession = async (sessionId: string) => {
  try {
    const result = await chatStore.restoreSession(sessionId);
    recoveryResults.value[sessionId] = {
      success: result.success,
      error: result.success ? undefined : '恢复失败',
      timestamp: new Date()
    };
  } catch (error) {
    recoveryResults.value[sessionId] = {
      success: false,
      error: error instanceof Error ? error.message : '未知错误',
      timestamp: new Date()
    };
  }
};

const batchRecover = async () => {
  if (selectedSessions.value.length === 0) return;
  
  isRecovering.value = true;
  try {
    const results = await chatStore.batchRestoreSessions(selectedSessions.value);
    
    // 更新恢复结果
    results.forEach((result, index) => {
      const sessionId = selectedSessions.value[index];
      recoveryResults.value[sessionId] = {
        success: result.success,
        error: result.success ? undefined : result.error,
        timestamp: new Date()
      };
    });
    
    // 清除选择
    selectedSessions.value = [];
  } finally {
    isRecovering.value = false;
  }
};

const checkIntegrity = async (sessionId: string) => {
  try {
    const result = await chatStore.checkSessionIntegrity(sessionId);
    console.log('完整性检查结果:', result);
    // TODO: 显示检查结果
  } catch (error) {
    console.error('完整性检查失败:', error);
  }
};

const viewSessionDetails = (session: RecoverableSession) => {
  selectedSessionDetails.value = session;
};

const closeDetails = () => {
  selectedSessionDetails.value = null;
};

const clearFailedRecoveries = () => {
  Object.keys(recoveryResults.value).forEach(sessionId => {
    if (!recoveryResults.value[sessionId].success) {
      delete recoveryResults.value[sessionId];
    }
  });
};

const getIntegrityClass = (session: RecoverableSession): string => {
  if (session.can_recover) return 'good';
  if (session.metadata_exists && session.state_consistent) return 'warning';
  return 'error';
};

const getIntegrityText = (session: RecoverableSession): string => {
  if (session.can_recover) return '完整';
  if (session.metadata_exists && session.state_consistent) return '部分';
  return '损坏';
};

const formatDate = (dateStr: string): string => {
  return new Date(dateStr).toLocaleDateString('zh-CN');
};

const formatDateTime = (dateStr: string): string => {
  return new Date(dateStr).toLocaleString('zh-CN');
};

const formatTime = (date: Date): string => {
  return date.toLocaleTimeString('zh-CN');
};

const formatRelativeTime = (dateStr: string): string => {
  const date = new Date(dateStr);
  const now = new Date();
  const diff = now.getTime() - date.getTime();
  const hours = Math.floor(diff / (1000 * 60 * 60));
  const days = Math.floor(diff / (1000 * 60 * 60 * 24));

  if (hours < 1) return '刚刚';
  if (hours < 24) return `${hours} 小时前`;
  if (days < 7) return `${days} 天前`;
  return formatDate(dateStr);
};

// 生命周期
onMounted(() => {
  refreshRecoverableSessions();
});
</script>
