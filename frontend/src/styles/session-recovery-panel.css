/* 会话恢复面板样式 */
.session-recovery-panel {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow: hidden;
}

/* 头部 */
.recovery-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.recovery-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 4px;
  background: #f8f9fa;
  color: #6c757d;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e9ecef;
  color: #495057;
}

/* 恢复统计 */
.recovery-stats {
  display: flex;
  gap: 20px;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.stat-item {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 24px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #6c757d;
}

/* 工具栏 */
.recovery-toolbar {
  display: flex;
  gap: 8px;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
}

.toolbar-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #ffffff;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toolbar-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.toolbar-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.toolbar-btn.batch-btn {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.toolbar-btn.batch-btn:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 列表容器 */
.recovery-list-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6c757d;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e9ecef;
  border-top: 3px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.empty-state h4 {
  margin: 0 0 8px 0;
  font-size: 18px;
  color: #495057;
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

/* 恢复列表 */
.recovery-list {
  padding: 16px 0;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #e9ecef;
  border-radius: 6px;
  margin-bottom: 16px;
}

.select-all {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
}

.session-count {
  font-size: 14px;
  color: #6c757d;
}

/* 恢复项目 */
.recovery-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  margin-bottom: 8px;
  background: #ffffff;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.recovery-item:hover {
  border-color: #007bff;
  box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
}

.recovery-item.selected {
  border-color: #007bff;
  background: #f8f9ff;
}

.recovery-item.recovering {
  border-color: #ffc107;
  background: #fffbf0;
}

.recovery-item.recovered {
  border-color: #28a745;
  background: #f8fff9;
}

.recovery-item.failed {
  border-color: #dc3545;
  background: #fff8f8;
}

.session-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.session-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #2c3e50;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.session-badges {
  display: flex;
  gap: 6px;
}

.integrity-badge,
.recoverable-badge {
  font-size: 11px;
  padding: 2px 6px;
  border-radius: 10px;
  font-weight: 500;
}

.integrity-badge.good {
  background: #d4edda;
  color: #155724;
}

.integrity-badge.warning {
  background: #fff3cd;
  color: #856404;
}

.integrity-badge.error {
  background: #f8d7da;
  color: #721c24;
}

.recoverable-badge {
  background: #cce5ff;
  color: #004085;
}

.session-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

/* 恢复进度 */
.recovery-progress {
  margin-top: 8px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background: #e9ecef;
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 4px;
}

.progress-fill {
  height: 100%;
  background: #007bff;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: #6c757d;
}

/* 恢复结果 */
.recovery-result {
  margin-top: 8px;
  font-size: 12px;
}

.success-result {
  color: #28a745;
  display: flex;
  align-items: center;
  gap: 4px;
}

.error-result {
  color: #dc3545;
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 会话操作 */
.session-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 6px 12px;
  border: 1px solid #dee2e6;
  border-radius: 4px;
  background: #ffffff;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.action-btn.recover-btn {
  background: #28a745;
  color: #ffffff;
  border-color: #28a745;
}

.action-btn.recover-btn:hover:not(:disabled) {
  background: #1e7e34;
  border-color: #1e7e34;
}

/* 详情对话框 */
.details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.details-dialog {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.details-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e9ecef;
}

.details-header h4 {
  margin: 0;
  font-size: 18px;
  color: #2c3e50;
}

.details-content {
  padding: 24px;
}

.detail-section {
  margin-bottom: 24px;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #495057;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.detail-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.detail-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.detail-item label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.detail-item span {
  font-size: 14px;
  color: #2c3e50;
}

.integrity-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.integrity-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 4px;
  background: #f8d7da;
  color: #721c24;
}

.integrity-item.valid {
  background: #d4edda;
  color: #155724;
}

.error-message {
  padding: 12px;
  background: #f8d7da;
  color: #721c24;
  border-radius: 4px;
  font-size: 14px;
  font-family: monospace;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recovery-stats {
    flex-direction: column;
    gap: 12px;
  }
  
  .recovery-toolbar {
    flex-direction: column;
  }
  
  .recovery-item {
    flex-direction: column;
    align-items: stretch;
  }
  
  .session-actions {
    justify-content: center;
    margin-top: 12px;
  }
  
  .detail-grid {
    grid-template-columns: 1fr;
  }
}
