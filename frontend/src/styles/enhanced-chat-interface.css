/* Enhanced Chat Interface Styles */

.enhanced-chat-interface {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--devui-base-bg, #ffffff);
}

/* 聊天头部 */
.chat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
  background: var(--devui-base-bg, #ffffff);
  position: sticky;
  top: 0;
  z-index: 10;
}

.session-info {
  flex: 1;
}

.session-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--devui-text, #252b3a);
  line-height: 1.4;
}

.session-meta {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: var(--devui-text-weak, #8a8e99);
}

.message-count,
.last-activity {
  display: flex;
  align-items: center;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--devui-text-weak, #8a8e99);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.action-btn:hover {
  background: var(--devui-list-item-hover-bg, #f2f5fc);
  color: var(--devui-brand, #5e7ce0);
}

.action-btn.active {
  background: var(--devui-brand-active, #3d5afe);
  color: white;
}

.action-btn.small {
  width: 28px;
  height: 28px;
  font-size: 14px;
}

/* 搜索栏 */
.search-bar {
  padding: 12px 20px;
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
  background: var(--devui-global-bg, #f8f9fa);
}

.search-input-wrapper {
  display: flex;
  align-items: center;
  background: white;
  border: 1px solid var(--devui-form-control-line, #d7d8da);
  border-radius: 8px;
  overflow: hidden;
}

.search-input {
  flex: 1;
  padding: 8px 12px;
  border: none;
  outline: none;
  font-size: 14px;
  background: transparent;
}

.search-btn {
  padding: 8px 12px;
  border: none;
  background: var(--devui-brand, #5e7ce0);
  color: white;
  cursor: pointer;
  transition: background-color 0.2s;
}

.search-btn:hover {
  background: var(--devui-brand-hover, #7693f5);
}

.search-results-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 8px;
  padding: 4px 8px;
  font-size: 12px;
  color: var(--devui-text-weak, #8a8e99);
  background: var(--devui-warning-bg, #fff7e6);
  border-radius: 4px;
}

.clear-search-btn {
  background: none;
  border: none;
  color: var(--devui-text-weak, #8a8e99);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.clear-search-btn:hover {
  background: var(--devui-list-item-hover-bg, #f2f5fc);
}

/* 消息容器 */
.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 0 20px;
  scroll-behavior: smooth;
}

.messages-container.search-active {
  background: var(--devui-global-bg, #f8f9fa);
}

/* 加载更多历史 */
.load-more-history {
  padding: 16px 0;
  text-align: center;
}

.load-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  border: 1px solid var(--devui-dividing-line, #e8e8e8);
  border-radius: 20px;
  background: white;
  color: var(--devui-text, #252b3a);
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.load-more-btn:hover:not(:disabled) {
  border-color: var(--devui-brand, #5e7ce0);
  color: var(--devui-brand, #5e7ce0);
}

.load-more-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 消息列表 */
.messages-list {
  padding: 20px 0;
}

.message-wrapper {
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.message-wrapper.highlighted {
  background: var(--devui-warning-bg, #fff7e6);
  border-radius: 8px;
  padding: 8px;
  margin: 8px -8px;
}

/* 时间戳分隔线 */
.timestamp-divider {
  text-align: center;
  margin: 20px 0 16px 0;
  position: relative;
  font-size: 12px;
  color: var(--devui-text-weak, #8a8e99);
}

.timestamp-divider::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: var(--devui-dividing-line, #e8e8e8);
  z-index: 1;
}

.timestamp-divider::after {
  content: attr(data-time);
  background: var(--devui-base-bg, #ffffff);
  padding: 0 12px;
  position: relative;
  z-index: 2;
}

/* 消息气泡增强 */
.message-bubble {
  position: relative;
}

.ai-bubble .ai-content {
  position: relative;
}

.message-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-wrapper:hover .message-actions {
  opacity: 1;
}

/* AI状态样式 */
.ai-status {
  padding: 12px;
  border-radius: 8px;
  background: var(--devui-global-bg, #f8f9fa);
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.status-item:last-child {
  margin-bottom: 0;
}

.status-icon {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.status-text {
  font-size: 14px;
  color: var(--devui-text, #252b3a);
}

.tool-calls {
  flex: 1;
}

.tool-call {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  margin: 4px 0;
  background: white;
  border-radius: 4px;
  font-size: 12px;
}

.tool-name {
  font-weight: 500;
  color: var(--devui-text, #252b3a);
}

.tool-status {
  display: flex;
  align-items: center;
  font-size: 12px;
}

.tool-status.calling {
  color: var(--devui-warning, #fa9841);
}

.tool-status.success {
  color: var(--devui-success, #50d4ab);
}

.tool-status.error {
  color: var(--devui-danger, #f66f6a);
}

/* 滚动到底部按钮 */
.scroll-to-bottom {
  position: fixed;
  bottom: 120px;
  right: 40px;
  z-index: 100;
}

.scroll-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: var(--devui-brand, #5e7ce0);
  color: white;
  border: none;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  transition: all 0.2s ease;
}

.scroll-btn:hover {
  background: var(--devui-brand-hover, #7693f5);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* 输入区域 */
.input-area {
  border-top: 1px solid var(--devui-dividing-line, #e8e8e8);
  background: var(--devui-base-bg, #ffffff);
}

/* 搜索高亮 */
mark {
  background: var(--devui-warning-bg, #fff7e6);
  color: var(--devui-warning, #fa9841);
  padding: 1px 2px;
  border-radius: 2px;
}

/* 图标样式 */
.icon-loading.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 全屏模式 */
:global(.chat-fullscreen) .enhanced-chat-interface {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  background: var(--devui-base-bg, #ffffff);
}

/* Enhanced Chat Input Styles */

.enhanced-chat-input {
  background: var(--devui-base-bg, #ffffff);
  border-top: 1px solid var(--devui-dividing-line, #e8e8e8);
}

/* 快捷操作栏 */
.quick-actions-bar {
  padding: 12px 20px 0;
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
}

.quick-actions-scroll {
  display: flex;
  gap: 8px;
  overflow-x: auto;
  padding-bottom: 8px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: 1px solid var(--devui-dividing-line, #e8e8e8);
  border-radius: 16px;
  background: white;
  color: var(--devui-text, #252b3a);
  cursor: pointer;
  font-size: 12px;
  white-space: nowrap;
  transition: all 0.2s ease;
}

.quick-action-btn:hover {
  border-color: var(--devui-brand, #5e7ce0);
  color: var(--devui-brand, #5e7ce0);
  background: var(--devui-brand-bg, #f2f5fc);
}

/* 智能提示词 */
.smart-prompts {
  padding: 12px 20px;
  background: var(--devui-global-bg, #f8f9fa);
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
}

.prompts-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}

.prompts-title {
  font-size: 12px;
  font-weight: 500;
  color: var(--devui-text, #252b3a);
}

.toggle-prompts-btn {
  background: none;
  border: none;
  color: var(--devui-text-weak, #8a8e99);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.toggle-prompts-btn:hover {
  background: var(--devui-list-item-hover-bg, #f2f5fc);
}

/* 输入框容器 */
.input-wrapper {
  position: relative;
  padding: 20px;
}

.drag-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(94, 124, 224, 0.1);
  border: 2px dashed var(--devui-brand, #5e7ce0);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.drag-content {
  text-align: center;
  color: var(--devui-brand, #5e7ce0);
}

.drag-content i {
  font-size: 32px;
  margin-bottom: 8px;
}

/* 输入框底部 */
.input-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: var(--devui-global-bg, #f8f9fa);
  border-radius: 0 0 8px 8px;
}

.input-footer-left,
.input-footer-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-action-item {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: var(--devui-text-weak, #8a8e99);
  transition: all 0.2s ease;
}

.input-action-item:hover {
  background: var(--devui-list-item-hover-bg, #f2f5fc);
  color: var(--devui-brand, #5e7ce0);
}

.input-action-item.voice-input.active {
  background: var(--devui-danger-bg, #ffeaea);
  color: var(--devui-danger, #f66f6a);
}

.input-divider {
  width: 1px;
  height: 12px;
  background: var(--devui-dividing-line, #e8e8e8);
}

.char-count {
  font-size: 11px;
  color: var(--devui-text-weak, #8a8e99);
}

.char-count.warning {
  color: var(--devui-warning, #fa9841);
}

.toggle-actions-btn,
.clear-input-btn,
.send-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.toggle-actions-btn {
  background: transparent;
  color: var(--devui-text-weak, #8a8e99);
}

.toggle-actions-btn:hover,
.toggle-actions-btn.active {
  background: var(--devui-brand-bg, #f2f5fc);
  color: var(--devui-brand, #5e7ce0);
}

.clear-input-btn {
  background: transparent;
  color: var(--devui-text-weak, #8a8e99);
}

.clear-input-btn:hover:not(:disabled) {
  background: var(--devui-danger-bg, #ffeaea);
  color: var(--devui-danger, #f66f6a);
}

.clear-input-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.send-btn {
  background: var(--devui-brand, #5e7ce0);
  color: white;
}

.send-btn:hover:not(:disabled) {
  background: var(--devui-brand-hover, #7693f5);
}

.send-btn:disabled {
  background: var(--devui-disabled-bg, #f5f5f6);
  color: var(--devui-disabled-text, #c7c7cc);
  cursor: not-allowed;
}

.send-btn.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(94, 124, 224, 0.7); }
  70% { box-shadow: 0 0 0 10px rgba(94, 124, 224, 0); }
  100% { box-shadow: 0 0 0 0 rgba(94, 124, 224, 0); }
}

/* 输入提示 */
.input-hints {
  padding: 8px 20px;
  background: var(--devui-info-bg, #e8f4fd);
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
  display: flex;
  gap: 16px;
  font-size: 11px;
  color: var(--devui-text-weak, #8a8e99);
}

.hint-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

kbd {
  padding: 2px 4px;
  background: var(--devui-base-bg, #ffffff);
  border: 1px solid var(--devui-dividing-line, #e8e8e8);
  border-radius: 2px;
  font-size: 10px;
  font-family: monospace;
}

/* 上传文件 */
.uploaded-files {
  padding: 12px 20px;
  background: var(--devui-global-bg, #f8f9fa);
  border-bottom: 1px solid var(--devui-dividing-line, #e8e8e8);
}

.files-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 12px;
  font-weight: 500;
  color: var(--devui-text, #252b3a);
}

.clear-files-btn {
  background: none;
  border: none;
  color: var(--devui-text-weak, #8a8e99);
  cursor: pointer;
  padding: 2px;
  border-radius: 2px;
}

.clear-files-btn:hover {
  background: var(--devui-danger-bg, #ffeaea);
  color: var(--devui-danger, #f66f6a);
}

.files-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 12px;
  background: white;
  border: 1px solid var(--devui-dividing-line, #e8e8e8);
  border-radius: 6px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.file-icon {
  font-size: 16px;
  color: var(--devui-brand, #5e7ce0);
}

.file-name {
  font-size: 12px;
  color: var(--devui-text, #252b3a);
  font-weight: 500;
}

.file-size {
  font-size: 11px;
  color: var(--devui-text-weak, #8a8e99);
}

.remove-file-btn {
  background: none;
  border: none;
  color: var(--devui-text-weak, #8a8e99);
  cursor: pointer;
  padding: 4px;
  border-radius: 2px;
}

.remove-file-btn:hover {
  background: var(--devui-danger-bg, #ffeaea);
  color: var(--devui-danger, #f66f6a);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .chat-header {
    padding: 12px 16px;
  }

  .session-title {
    font-size: 16px;
  }

  .quick-actions {
    gap: 4px;
  }

  .action-btn {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }

  .messages-container {
    padding: 0 16px;
  }

  .search-bar {
    padding: 8px 16px;
  }

  .scroll-to-bottom {
    bottom: 100px;
    right: 20px;
  }

  .scroll-btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .message-actions {
    opacity: 1; /* 移动端始终显示 */
  }

  .input-wrapper {
    padding: 16px;
  }

  .quick-actions-bar {
    padding: 8px 16px 0;
  }

  .smart-prompts {
    padding: 8px 16px;
  }

  .uploaded-files {
    padding: 8px 16px;
  }

  .input-hints {
    padding: 6px 16px;
    flex-wrap: wrap;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .session-meta {
    flex-direction: column;
    gap: 4px;
  }

  .quick-actions {
    flex-wrap: wrap;
  }

  .search-results-info {
    font-size: 11px;
  }

  .input-footer {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .input-footer-left,
  .input-footer-right {
    justify-content: center;
  }

  .quick-action-btn {
    font-size: 11px;
    padding: 4px 8px;
  }
}
