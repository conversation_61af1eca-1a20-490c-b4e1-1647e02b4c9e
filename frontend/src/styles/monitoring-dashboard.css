/* 监控仪表板样式 */
.monitoring-dashboard {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f8f9fa;
  border-radius: 8px;
  overflow-y: auto;
}

/* 头部 */
.dashboard-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #ffffff;
  border-bottom: 1px solid #e9ecef;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: sticky;
  top: 0;
  z-index: 10;
}

.dashboard-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
  display: flex;
  align-items: center;
  gap: 8px;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.refresh-btn,
.auto-refresh-btn,
.close-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: #ffffff;
  color: #495057;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover:not(:disabled),
.auto-refresh-btn:hover,
.close-btn:hover {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.auto-refresh-btn.active {
  background: #007bff;
  color: #ffffff;
  border-color: #007bff;
}

.auto-refresh-btn.active:hover {
  background: #0056b3;
  border-color: #0056b3;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 系统健康状态 */
.health-overview {
  padding: 20px;
}

.health-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  border-left: 4px solid #6c757d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.health-card.health-good {
  border-left-color: #28a745;
}

.health-card.health-warning {
  border-left-color: #ffc107;
}

.health-card.health-error {
  border-left-color: #dc3545;
}

.health-icon {
  font-size: 32px;
}

.health-card.health-good .health-icon {
  color: #28a745;
}

.health-card.health-warning .health-icon {
  color: #ffc107;
}

.health-card.health-error .health-icon {
  color: #dc3545;
}

.health-info h4 {
  margin: 0 0 4px 0;
  font-size: 18px;
  color: #2c3e50;
}

.health-status {
  display: block;
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 4px;
}

.health-card.health-good .health-status {
  color: #28a745;
}

.health-card.health-warning .health-status {
  color: #ffc107;
}

.health-card.health-error .health-status {
  color: #dc3545;
}

.health-time {
  font-size: 12px;
  color: #6c757d;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  padding: 0 20px 20px;
}

.stat-card {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.2s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: #ffffff;
  background: linear-gradient(135deg, #007bff, #0056b3);
}

.stat-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #6c757d;
  font-weight: 500;
}

.stat-number {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 4px;
}

.stat-change {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
}

.stat-change.positive {
  color: #28a745;
}

.stat-change.negative {
  color: #dc3545;
}

.stat-change.neutral {
  color: #6c757d;
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  padding: 0 20px 20px;
}

.chart-card {
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
}

.chart-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.time-range-select {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  font-size: 12px;
  cursor: pointer;
}

.chart-content {
  padding: 20px;
}

.duration-stats,
.frequency-stats {
  display: flex;
  justify-content: space-around;
  text-align: center;
}

.duration-item,
.frequency-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.duration-label,
.frequency-label {
  font-size: 12px;
  color: #6c757d;
  font-weight: 500;
}

.duration-value,
.frequency-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 用户活跃度分析 */
.activity-section {
  margin: 0 20px 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e9ecef;
}

.activity-header h4 {
  margin: 0;
  font-size: 16px;
  color: #2c3e50;
}

.limit-select {
  padding: 6px 10px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background: #ffffff;
  font-size: 12px;
  cursor: pointer;
}

.activity-content {
  padding: 20px;
}

.user-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.user-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
}

.user-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #007bff;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
}

.user-info {
  flex: 1;
  min-width: 0;
}

.user-id {
  font-size: 14px;
  font-weight: 500;
  color: #2c3e50;
  margin-bottom: 2px;
}

.user-stats {
  font-size: 12px;
  color: #6c757d;
}

.user-activity {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 120px;
}

.activity-bar {
  flex: 1;
  height: 6px;
  background: #e9ecef;
  border-radius: 3px;
  overflow: hidden;
}

.activity-fill {
  height: 100%;
  background: linear-gradient(90deg, #007bff, #0056b3);
  transition: width 0.3s ease;
}

.activity-score {
  font-size: 12px;
  font-weight: 500;
  color: #495057;
  min-width: 40px;
  text-align: right;
}

.no-activity-data {
  text-align: center;
  padding: 40px 20px;
  color: #6c757d;
}

.no-activity-data i {
  font-size: 32px;
  margin-bottom: 12px;
  display: block;
}

/* 系统资源 */
.resources-section {
  margin: 0 20px 20px;
  background: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  padding: 20px;
}

.resources-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #2c3e50;
}

.resources-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.resource-item {
  text-align: center;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.resource-label {
  font-size: 12px;
  color: #6c757d;
  margin-bottom: 8px;
}

.resource-value {
  font-size: 18px;
  font-weight: 600;
  color: #2c3e50;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .dashboard-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .health-card {
    flex-direction: column;
    text-align: center;
  }
  
  .user-item {
    flex-direction: column;
    align-items: stretch;
    text-align: center;
  }
  
  .user-activity {
    justify-content: center;
  }
  
  .resources-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 480px) {
  .resources-grid {
    grid-template-columns: 1fr;
  }
  
  .duration-stats,
  .frequency-stats {
    flex-direction: column;
    gap: 16px;
  }
}
