/**
 * 会话恢复服务
 * 集成第二阶段的会话恢复功能，提供历史会话恢复和完整性检查
 */

import { chatAPI } from './api';
import type {
  RecoverableSession,
  RecoverableSessionsResponse,
  SessionContextRecoveryResponse,
  SessionIntegrityResponse
} from './api';

export interface RecoveryProgress {
  sessionId: string;
  status: 'preparing' | 'loading' | 'restoring' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
  error?: string;
}

export class SessionRecoveryService {
  private recoveryListeners: Map<string, (progress: RecoveryProgress) => void> = new Map();

  /**
   * 获取可恢复的会话列表
   */
  async getRecoverableSessions(userId: string, limit: number = 20): Promise<RecoverableSession[]> {
    try {
      const response = await chatAPI.getRecoverableSessions(userId, limit);
      return response.sessions;
    } catch (error) {
      console.error('获取可恢复会话列表失败:', error);
      throw error;
    }
  }

  /**
   * 恢复会话上下文
   */
  async restoreSessionContext(
    sessionId: string,
    onProgress?: (progress: RecoveryProgress) => void
  ): Promise<SessionContextRecoveryResponse> {
    try {
      // 注册进度监听器
      if (onProgress) {
        this.recoveryListeners.set(sessionId, onProgress);
      }

      // 开始恢复过程
      this.updateProgress(sessionId, {
        sessionId,
        status: 'preparing',
        progress: 0,
        message: '准备恢复会话...'
      });

      // 检查会话完整性
      this.updateProgress(sessionId, {
        sessionId,
        status: 'loading',
        progress: 20,
        message: '检查会话完整性...'
      });

      const integrityResult = await this.checkSessionIntegrity(sessionId);
      if (!integrityResult.valid) {
        throw new Error(`会话完整性检查失败: ${integrityResult.error || '数据不完整'}`);
      }

      // 开始恢复
      this.updateProgress(sessionId, {
        sessionId,
        status: 'restoring',
        progress: 50,
        message: '正在恢复会话上下文...'
      });

      const result = await chatAPI.restoreSessionContext(sessionId);

      if (result.success) {
        this.updateProgress(sessionId, {
          sessionId,
          status: 'complete',
          progress: 100,
          message: `会话恢复成功，恢复了 ${result.message_count || 0} 条消息`
        });
      } else {
        throw new Error(result.error || '恢复失败');
      }

      return result;
    } catch (error) {
      this.updateProgress(sessionId, {
        sessionId,
        status: 'error',
        progress: 0,
        message: '恢复失败',
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    } finally {
      // 清理监听器
      this.recoveryListeners.delete(sessionId);
    }
  }

  /**
   * 检查会话完整性
   */
  async checkSessionIntegrity(sessionId: string): Promise<SessionIntegrityResponse> {
    try {
      return await chatAPI.checkSessionIntegrity(sessionId);
    } catch (error) {
      console.error('检查会话完整性失败:', error);
      throw error;
    }
  }

  /**
   * 批量恢复会话
   */
  async batchRestoreSessions(
    sessionIds: string[],
    onProgress?: (sessionId: string, progress: RecoveryProgress) => void
  ): Promise<Map<string, SessionContextRecoveryResponse | Error>> {
    const results = new Map<string, SessionContextRecoveryResponse | Error>();

    for (const sessionId of sessionIds) {
      try {
        const result = await this.restoreSessionContext(
          sessionId,
          onProgress ? (progress) => onProgress(sessionId, progress) : undefined
        );
        results.set(sessionId, result);
      } catch (error) {
        results.set(sessionId, error instanceof Error ? error : new Error(String(error)));
      }
    }

    return results;
  }

  /**
   * 获取会话恢复建议
   */
  getRecoveryRecommendation(session: RecoverableSession): {
    priority: 'high' | 'medium' | 'low';
    reason: string;
    canRecover: boolean;
  } {
    const { recovery_score, message_count, can_recover } = session;

    if (!can_recover) {
      return {
        priority: 'low',
        reason: '会话数据不完整，无法恢复',
        canRecover: false
      };
    }

    if (recovery_score >= 0.8 && message_count >= 5) {
      return {
        priority: 'high',
        reason: '会话数据完整，包含重要对话内容',
        canRecover: true
      };
    }

    if (recovery_score >= 0.6 || message_count >= 3) {
      return {
        priority: 'medium',
        reason: '会话数据基本完整，可以恢复',
        canRecover: true
      };
    }

    return {
      priority: 'low',
      reason: '会话内容较少，恢复价值不高',
      canRecover: true
    };
  }

  /**
   * 格式化恢复分数
   */
  formatRecoveryScore(score: number): string {
    const percentage = Math.round(score * 100);
    if (percentage >= 80) return `${percentage}% (优秀)`;
    if (percentage >= 60) return `${percentage}% (良好)`;
    if (percentage >= 40) return `${percentage}% (一般)`;
    return `${percentage}% (较差)`;
  }

  /**
   * 获取恢复状态颜色
   */
  getRecoveryStatusColor(status: RecoveryProgress['status']): string {
    switch (status) {
      case 'complete':
        return '#52c41a'; // 绿色
      case 'restoring':
      case 'loading':
        return '#1890ff'; // 蓝色
      case 'preparing':
        return '#faad14'; // 橙色
      case 'error':
        return '#f5222d'; // 红色
      default:
        return '#d9d9d9'; // 灰色
    }
  }

  /**
   * 更新恢复进度
   */
  private updateProgress(sessionId: string, progress: RecoveryProgress): void {
    const listener = this.recoveryListeners.get(sessionId);
    if (listener) {
      listener(progress);
    }
  }

  /**
   * 估算恢复时间
   */
  estimateRecoveryTime(messageCount: number): string {
    // 基于消息数量估算恢复时间
    const baseTime = 2; // 基础时间2秒
    const timePerMessage = 0.1; // 每条消息0.1秒
    const totalSeconds = baseTime + (messageCount * timePerMessage);

    if (totalSeconds < 60) {
      return `约 ${Math.round(totalSeconds)} 秒`;
    } else {
      const minutes = Math.floor(totalSeconds / 60);
      const seconds = Math.round(totalSeconds % 60);
      return `约 ${minutes} 分 ${seconds} 秒`;
    }
  }

  /**
   * 验证会话是否可以安全恢复
   */
  async validateRecoveryConditions(sessionId: string): Promise<{
    canRecover: boolean;
    issues: string[];
    warnings: string[];
  }> {
    const issues: string[] = [];
    const warnings: string[] = [];

    try {
      const integrity = await this.checkSessionIntegrity(sessionId);
      
      if (!integrity.valid) {
        issues.push('会话数据完整性检查失败');
      }

      if (!integrity.metadata_exists) {
        issues.push('会话元数据缺失');
      }

      if ((integrity.message_count || 0) === 0) {
        warnings.push('会话没有消息历史');
      }

      if (!integrity.state_consistent) {
        warnings.push('LangGraph状态可能不一致');
      }

      return {
        canRecover: issues.length === 0,
        issues,
        warnings
      };
    } catch (error) {
      return {
        canRecover: false,
        issues: [`检查恢复条件失败: ${error instanceof Error ? error.message : String(error)}`],
        warnings: []
      };
    }
  }
}

// 导出单例实例
export const sessionRecoveryService = new SessionRecoveryService();
