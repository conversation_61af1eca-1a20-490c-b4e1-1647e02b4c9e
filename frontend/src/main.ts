import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import './style.css';

// 引入新组件的样式
import './styles/enhanced-session-manager.css';
import './styles/session-recovery-panel.css';
import './styles/monitoring-dashboard.css';
import './styles/enhanced-chat-interface.css';

// 引入MateChat
import MateChat from '@matechat/core';

const app = createApp(App);
const pinia = createPinia();

app.use(pinia);
app.use(MateChat);

app.mount('#app');
