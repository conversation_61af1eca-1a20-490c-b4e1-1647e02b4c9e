/**
 * 会话监控服务
 * 集成第二阶段的监控功能，提供实时系统状态和用户活跃度分析
 */

import { chatAPI } from './api';
import type {
  ActiveSessionsStats,
  SessionDurationAnalysis,
  MessageFrequencyStats,
  UserActivityAnalysis,
  SystemHealthMetrics
} from './api';

export interface MonitoringDashboardData {
  activeSessionsStats: ActiveSessionsStats;
  sessionDurationAnalysis: SessionDurationAnalysis;
  messageFrequencyStats: MessageFrequencyStats;
  userActivityAnalysis: UserActivityAnalysis;
  systemHealthMetrics: SystemHealthMetrics;
  lastUpdated: string;
}

export class MonitoringService {
  private updateInterval: number = 30000; // 30秒更新一次
  private intervalId: number | null = null;
  private listeners: ((data: MonitoringDashboardData) => void)[] = [];

  /**
   * 获取完整的监控仪表板数据
   */
  async getDashboardData(): Promise<MonitoringDashboardData> {
    try {
      const [
        activeSessionsStats,
        sessionDurationAnalysis,
        messageFrequencyStats,
        userActivityAnalysis,
        systemHealthMetrics
      ] = await Promise.all([
        chatAPI.getActiveSessionsStats(24), // 24小时窗口
        chatAPI.getSessionDurationAnalysis(100),
        chatAPI.getMessageFrequencyStats(7), // 7天统计
        chatAPI.getUserActivityAnalysis(50),
        chatAPI.getSystemHealthMetrics()
      ]);

      return {
        activeSessionsStats,
        sessionDurationAnalysis,
        messageFrequencyStats,
        userActivityAnalysis,
        systemHealthMetrics,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('获取监控数据失败:', error);
      throw error;
    }
  }

  /**
   * 获取活跃会话统计
   */
  async getActiveSessionsStats(timeWindowHours: number = 24): Promise<ActiveSessionsStats> {
    return await chatAPI.getActiveSessionsStats(timeWindowHours);
  }

  /**
   * 获取会话持续时间分析
   */
  async getSessionDurationAnalysis(limit: number = 100): Promise<SessionDurationAnalysis> {
    return await chatAPI.getSessionDurationAnalysis(limit);
  }

  /**
   * 获取消息频率统计
   */
  async getMessageFrequencyStats(days: number = 7): Promise<MessageFrequencyStats> {
    return await chatAPI.getMessageFrequencyStats(days);
  }

  /**
   * 获取用户活跃度分析
   */
  async getUserActivityAnalysis(limit: number = 50): Promise<UserActivityAnalysis> {
    return await chatAPI.getUserActivityAnalysis(limit);
  }

  /**
   * 获取系统健康指标
   */
  async getSystemHealthMetrics(): Promise<SystemHealthMetrics> {
    return await chatAPI.getSystemHealthMetrics();
  }

  /**
   * 开始实时监控
   */
  startRealTimeMonitoring(callback: (data: MonitoringDashboardData) => void): void {
    this.listeners.push(callback);
    
    if (this.intervalId === null) {
      // 立即获取一次数据
      this.updateMonitoringData();
      
      // 设置定时更新
      this.intervalId = window.setInterval(() => {
        this.updateMonitoringData();
      }, this.updateInterval);
    }
  }

  /**
   * 停止实时监控
   */
  stopRealTimeMonitoring(callback?: (data: MonitoringDashboardData) => void): void {
    if (callback) {
      this.listeners = this.listeners.filter(listener => listener !== callback);
    } else {
      this.listeners = [];
    }

    if (this.listeners.length === 0 && this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * 设置更新间隔
   */
  setUpdateInterval(intervalMs: number): void {
    this.updateInterval = intervalMs;
    
    if (this.intervalId !== null) {
      clearInterval(this.intervalId);
      this.intervalId = window.setInterval(() => {
        this.updateMonitoringData();
      }, this.updateInterval);
    }
  }

  /**
   * 更新监控数据并通知所有监听器
   */
  private async updateMonitoringData(): Promise<void> {
    try {
      const data = await this.getDashboardData();
      this.listeners.forEach(listener => listener(data));
    } catch (error) {
      console.error('更新监控数据失败:', error);
    }
  }

  /**
   * 格式化数据大小
   */
  formatDataSize(sizeInMB: number): string {
    if (sizeInMB < 1) {
      return `${(sizeInMB * 1024).toFixed(1)} KB`;
    } else if (sizeInMB < 1024) {
      return `${sizeInMB.toFixed(1)} MB`;
    } else {
      return `${(sizeInMB / 1024).toFixed(1)} GB`;
    }
  }

  /**
   * 格式化持续时间
   */
  formatDuration(minutes: number): string {
    if (minutes < 60) {
      return `${Math.round(minutes)} 分钟`;
    } else if (minutes < 1440) {
      const hours = Math.floor(minutes / 60);
      const remainingMinutes = Math.round(minutes % 60);
      return `${hours} 小时 ${remainingMinutes} 分钟`;
    } else {
      const days = Math.floor(minutes / 1440);
      const remainingHours = Math.floor((minutes % 1440) / 60);
      return `${days} 天 ${remainingHours} 小时`;
    }
  }

  /**
   * 格式化百分比
   */
  formatPercentage(value: number): string {
    return `${(value * 100).toFixed(1)}%`;
  }

  /**
   * 获取系统状态颜色
   */
  getSystemStatusColor(status: string): string {
    switch (status.toLowerCase()) {
      case 'healthy':
        return '#52c41a'; // 绿色
      case 'warning':
        return '#faad14'; // 橙色
      case 'error':
      case 'critical':
        return '#f5222d'; // 红色
      default:
        return '#d9d9d9'; // 灰色
    }
  }

  /**
   * 获取活跃度等级
   */
  getActivityLevel(rate: number): string {
    if (rate >= 0.8) return '非常活跃';
    if (rate >= 0.6) return '活跃';
    if (rate >= 0.4) return '一般';
    if (rate >= 0.2) return '较低';
    return '很低';
  }
}

// 导出单例实例
export const monitoringService = new MonitoringService();
