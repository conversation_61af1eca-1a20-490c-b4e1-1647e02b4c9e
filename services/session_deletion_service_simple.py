"""
简化版会话删除服务 - 第二阶段任务2.2
提供基本的会话删除功能，包括软删除、硬删除和恢复
"""

import sqlite3
from typing import Dict, List, Optional, Any
from datetime import datetime
import logging
import json

from services.enhanced_data_layer import EnhancedDataLayer


class SessionDeletionService:
    """简化版会话删除服务"""
    
    def __init__(self, data_layer: EnhancedDataLayer):
        self.data_layer = data_layer
        self.db_path = data_layer.db_path
        self.logger = logging.getLogger(__name__)
    
    async def soft_delete_session(
        self, 
        session_id: str, 
        reason: Optional[str] = None,
        metadata: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """软删除会话"""
        try:
            # 检查会话是否存在
            session = await self.data_layer.get_session(session_id)
            if not session:
                raise ValueError(f"会话不存在: {session_id}")
            
            # 检查会话是否已被删除
            if session.get("is_deleted", False):
                raise ValueError(f"会话已被删除: {session_id}")
            
            # 准备删除信息
            deletion_info = {
                "deleted_at": datetime.now().isoformat(),
                "deletion_reason": reason or "用户删除",
                "deletion_metadata": metadata or {},
                "original_title": session["title"],
                "message_count_at_deletion": session["message_count"]
            }
            
            # 执行软删除 - 添加删除标记字段到session_metadata表
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 首先检查表结构，如果没有删除相关字段则添加
                cursor.execute("PRAGMA table_info(session_metadata)")
                columns = [row[1] for row in cursor.fetchall()]
                
                if 'is_deleted' not in columns:
                    cursor.execute("ALTER TABLE session_metadata ADD COLUMN is_deleted INTEGER DEFAULT 0")
                if 'deleted_at' not in columns:
                    cursor.execute("ALTER TABLE session_metadata ADD COLUMN deleted_at TEXT")
                if 'deletion_info' not in columns:
                    cursor.execute("ALTER TABLE session_metadata ADD COLUMN deletion_info TEXT")
                
                # 执行软删除
                cursor.execute("""
                    UPDATE session_metadata 
                    SET is_deleted = 1,
                        deleted_at = ?,
                        deletion_info = ?,
                        updated_at = ?
                    WHERE id = ?
                """, (
                    deletion_info["deleted_at"],
                    json.dumps(deletion_info),
                    datetime.now().isoformat(),
                    session_id
                ))
                
                if cursor.rowcount == 0:
                    raise ValueError(f"更新会话失败: {session_id}")
                
                conn.commit()
                
            finally:
                conn.close()
            
            self.logger.info(f"软删除会话成功: {session_id}, 原因: {reason}")
            
            return {
                "session_id": session_id,
                "deletion_type": "soft",
                "deleted": True,
                "can_recover": True,
                "deletion_info": deletion_info
            }
            
        except Exception as e:
            self.logger.error(f"软删除会话失败: {session_id}, 错误: {e}")
            raise
    
    async def hard_delete_session(
        self, 
        session_id: str,
        confirm_deletion: bool = False
    ) -> Dict[str, Any]:
        """硬删除会话（完全删除）"""
        if not confirm_deletion:
            raise ValueError("硬删除需要明确确认，请设置 confirm_deletion=True")
        
        try:
            # 检查会话是否存在
            session = await self.data_layer.get_session(session_id)
            if not session:
                raise ValueError(f"会话不存在: {session_id}")
            
            # 获取删除前的统计信息
            stats = self._get_deletion_stats(session_id)
            
            # 执行级联删除
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                
                # 删除消息
                cursor.execute("DELETE FROM messages WHERE session_id = ?", (session_id,))
                
                # 删除检查点数据（如果存在）
                try:
                    cursor.execute("DELETE FROM checkpoints WHERE thread_id = ?", (session_id,))
                    cursor.execute("DELETE FROM checkpoint_writes WHERE thread_id = ?", (session_id,))
                except sqlite3.OperationalError:
                    # 如果表不存在，忽略错误
                    pass
                
                # 删除会话
                cursor.execute("DELETE FROM session_metadata WHERE id = ?", (session_id,))
                
                conn.commit()
                
            finally:
                conn.close()
            
            self.logger.warning(f"硬删除会话成功: {session_id}, 删除了 {stats['message_count']} 条消息")
            
            return {
                "session_id": session_id,
                "deletion_type": "hard",
                "deleted": True,
                "can_recover": False,
                "deleted_data": stats
            }
            
        except Exception as e:
            self.logger.error(f"硬删除会话失败: {session_id}, 错误: {e}")
            raise
    
    async def recover_session(self, session_id: str) -> Dict[str, Any]:
        """恢复软删除的会话"""
        try:
            # 检查会话是否存在且被软删除
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT id, title, is_deleted, deleted_at, deletion_info
                    FROM session_metadata 
                    WHERE id = ?
                """, (session_id,))
                session_row = cursor.fetchone()
            finally:
                conn.close()
            
            if not session_row:
                raise ValueError(f"会话不存在: {session_id}")
            
            if not session_row[2]:  # is_deleted
                raise ValueError(f"会话未被删除，无需恢复: {session_id}")
            
            # 执行恢复
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute("""
                    UPDATE session_metadata 
                    SET is_deleted = 0,
                        deleted_at = NULL,
                        deletion_info = NULL,
                        updated_at = ?
                    WHERE id = ?
                """, (datetime.now().isoformat(), session_id))
                
                if cursor.rowcount == 0:
                    raise ValueError(f"恢复会话失败: {session_id}")
                
                conn.commit()
            finally:
                conn.close()
            
            self.logger.info(f"恢复会话成功: {session_id}")
            
            return {
                "session_id": session_id,
                "recovered": True,
                "recovery_time": datetime.now().isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"恢复会话失败: {session_id}, 错误: {e}")
            raise
    
    async def batch_delete_sessions(
        self,
        session_ids: List[str],
        deletion_type: str = "soft",
        reason: Optional[str] = None
    ) -> Dict[str, Any]:
        """批量删除会话"""
        results = {
            "total_requested": len(session_ids),
            "successful_deletions": [],
            "failed_deletions": [],
            "deletion_type": deletion_type
        }
        
        for session_id in session_ids:
            try:
                if deletion_type == "soft":
                    result = await self.soft_delete_session(session_id, reason)
                elif deletion_type == "hard":
                    result = await self.hard_delete_session(session_id, confirm_deletion=True)
                else:
                    raise ValueError(f"无效的删除类型: {deletion_type}")
                
                results["successful_deletions"].append({
                    "session_id": session_id,
                    "result": result
                })
                
            except Exception as e:
                results["failed_deletions"].append({
                    "session_id": session_id,
                    "error": str(e)
                })
        
        self.logger.info(f"批量删除完成: 成功 {len(results['successful_deletions'])}, 失败 {len(results['failed_deletions'])}")
        
        return results
    
    async def get_deleted_sessions(
        self,
        user_id: Optional[str] = None,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """获取已删除的会话列表"""
        try:
            query = """
                SELECT s.id, s.title, s.user_id, s.deleted_at, s.deletion_info,
                       u.display_name as user_name
                FROM session_metadata s
                LEFT JOIN users u ON s.user_id = u.id
                WHERE s.is_deleted = 1
            """
            params = []
            
            if user_id:
                query += " AND s.user_id = ?"
                params.append(user_id)
            
            query += " ORDER BY s.deleted_at DESC LIMIT ?"
            params.append(limit)
            
            conn = sqlite3.connect(self.db_path)
            try:
                cursor = conn.cursor()
                cursor.execute(query, params)
                rows = cursor.fetchall()
            finally:
                conn.close()
            
            deleted_sessions = []
            for row in rows:
                deleted_sessions.append({
                    "id": row[0],
                    "title": row[1],
                    "user_id": row[2],
                    "deleted_at": row[3],
                    "deletion_info": json.loads(row[4]) if row[4] else {},
                    "user_name": row[5]
                })
            
            return deleted_sessions
            
        except Exception as e:
            self.logger.error(f"获取已删除会话失败: {e}")
            raise
    
    def _get_deletion_stats(self, session_id: str) -> Dict[str, Any]:
        """获取删除统计信息"""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.cursor()
            # 获取消息数量
            cursor.execute(
                "SELECT COUNT(*) FROM messages WHERE session_id = ?",
                (session_id,)
            )
            message_count = cursor.fetchone()[0]
            
            return {
                "message_count": message_count,
                "deletion_time": datetime.now().isoformat()
            }
        finally:
            conn.close()


# 全局服务实例
_deletion_service: Optional[SessionDeletionService] = None


async def get_deletion_service() -> SessionDeletionService:
    """获取会话删除服务实例"""
    global _deletion_service
    if _deletion_service is None:
        from services.enhanced_data_layer import get_data_layer
        data_layer = await get_data_layer()
        _deletion_service = SessionDeletionService(data_layer)
    return _deletion_service
