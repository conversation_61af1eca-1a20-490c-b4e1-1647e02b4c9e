"""
数据库迁移脚本 - 扩展现有数据库以支持增强的会话管理
借鉴 LangGraphChainlitAgent 的数据库设计，与现有 LangGraph 检查点系统兼容
"""

import sqlite3
import json
import uuid
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class DatabaseMigration:
    """数据库迁移管理器"""
    
    def __init__(self, existing_db_path: str = "./data/agent_memory.db", 
                 enhanced_db_path: str = "./data/enhanced_sessions.db"):
        self.existing_db_path = Path(existing_db_path)
        self.enhanced_db_path = Path(enhanced_db_path)
        
        # 确保目录存在
        self.existing_db_path.parent.mkdir(parents=True, exist_ok=True)
        self.enhanced_db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def check_existing_database(self) -> bool:
        """检查现有数据库是否存在"""
        return self.existing_db_path.exists()
    
    def get_existing_tables(self) -> list:
        """获取现有数据库的表结构"""
        if not self.check_existing_database():
            return []
        
        conn = sqlite3.connect(self.existing_db_path)
        cursor = conn.cursor()
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            return tables
        finally:
            conn.close()
    
    def analyze_existing_checkpoints(self) -> dict:
        """分析现有的 LangGraph 检查点数据"""
        if not self.check_existing_database():
            return {"total_checkpoints": 0, "unique_threads": 0, "sample_threads": []}
        
        conn = sqlite3.connect(self.existing_db_path)
        cursor = conn.cursor()
        try:
            # 检查是否有 checkpoints 表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='checkpoints'")
            if not cursor.fetchone():
                return {"total_checkpoints": 0, "unique_threads": 0, "sample_threads": []}
            
            # 获取检查点统计
            cursor.execute("SELECT COUNT(*) FROM checkpoints")
            total_checkpoints = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(DISTINCT thread_id) FROM checkpoints")
            unique_threads = cursor.fetchone()[0]
            
            # 获取一些示例线程ID
            cursor.execute("SELECT DISTINCT thread_id FROM checkpoints LIMIT 10")
            sample_threads = [row[0] for row in cursor.fetchall()]
            
            return {
                "total_checkpoints": total_checkpoints,
                "unique_threads": unique_threads,
                "sample_threads": sample_threads
            }
        except Exception as e:
            logger.error(f"分析检查点数据失败: {e}")
            return {"total_checkpoints": 0, "unique_threads": 0, "sample_threads": []}
        finally:
            conn.close()
    
    def extract_thread_info_from_checkpoints(self) -> list:
        """从现有检查点中提取线程信息"""
        if not self.check_existing_database():
            return []
        
        conn = sqlite3.connect(self.existing_db_path)
        cursor = conn.cursor()
        try:
            # 检查是否有 checkpoints 表
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='checkpoints'")
            if not cursor.fetchone():
                return []
            
            # 获取每个线程的最早和最晚检查点
            cursor.execute("""
                SELECT 
                    thread_id,
                    MIN(checkpoint_id) as first_checkpoint,
                    MAX(checkpoint_id) as last_checkpoint,
                    COUNT(*) as checkpoint_count
                FROM checkpoints 
                GROUP BY thread_id
                ORDER BY MIN(checkpoint_id)
            """)
            
            threads_info = []
            for row in cursor.fetchall():
                thread_id, first_checkpoint, last_checkpoint, checkpoint_count = row
                
                # 尝试从第一个检查点中提取消息信息
                cursor.execute("SELECT checkpoint FROM checkpoints WHERE thread_id = ? ORDER BY checkpoint_id LIMIT 1", (thread_id,))
                first_checkpoint_data = cursor.fetchone()
                
                first_message = None
                if first_checkpoint_data:
                    try:
                        checkpoint_blob = first_checkpoint_data[0]
                        # 这里可能需要根据实际的检查点格式来解析
                        # LangGraph 的检查点通常包含消息历史
                        if isinstance(checkpoint_blob, (str, bytes)):
                            # 尝试解析检查点数据以提取第一条用户消息
                            # 这是一个简化的实现，实际可能需要更复杂的解析
                            if "messages" in str(checkpoint_blob):
                                first_message = f"来自线程 {thread_id[:8]}... 的对话"
                    except Exception as e:
                        logger.debug(f"解析检查点数据失败: {e}")
                
                threads_info.append({
                    "thread_id": thread_id,
                    "first_checkpoint": first_checkpoint,
                    "last_checkpoint": last_checkpoint,
                    "checkpoint_count": checkpoint_count,
                    "estimated_first_message": first_message or f"历史对话 {thread_id[:8]}..."
                })
            
            return threads_info
        except Exception as e:
            logger.error(f"提取线程信息失败: {e}")
            return []
        finally:
            conn.close()
    
    def migrate_to_enhanced_schema(self, default_user_id: str = "default_user") -> bool:
        """迁移到增强的数据库架构"""
        try:
            # 分析现有数据
            existing_analysis = self.analyze_existing_checkpoints()
            logger.info(f"现有数据分析: {existing_analysis}")
            
            # 创建增强数据层实例（这会自动创建新的表结构）
            from services.enhanced_data_layer import EnhancedDataLayer
            enhanced_layer = EnhancedDataLayer(str(self.enhanced_db_path))
            
            # 创建默认用户
            import asyncio

            # 检查是否已有运行的事件循环
            try:
                loop = asyncio.get_running_loop()
                # 如果有运行的循环，使用 run_in_executor
                def _create_user_sync():
                    new_loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(new_loop)
                    try:
                        return new_loop.run_until_complete(
                            enhanced_layer.create_user(
                                identifier=default_user_id,
                                display_name="默认用户",
                                metadata={"migrated_from": "langgraph_checkpoints"}
                            )
                        )
                    finally:
                        new_loop.close()

                import concurrent.futures
                with concurrent.futures.ThreadPoolExecutor() as executor:
                    future = executor.submit(_create_user_sync)
                    default_user = future.result()

            except RuntimeError:
                # 没有运行的事件循环，创建新的
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                try:
                    default_user = loop.run_until_complete(
                        enhanced_layer.create_user(
                            identifier=default_user_id,
                            display_name="默认用户",
                            metadata={"migrated_from": "langgraph_checkpoints"}
                        )
                    )
                finally:
                    if 'loop' in locals():
                        loop.close()
                logger.info(f"创建默认用户: {default_user}")
                
                # 提取现有线程信息
                threads_info = self.extract_thread_info_from_checkpoints()
                logger.info(f"发现 {len(threads_info)} 个历史线程")
                
                # 为每个历史线程创建会话元数据
                migrated_sessions = []
                for thread_info in threads_info:
                    try:
                        # 生成智能标题
                        title = loop.run_until_complete(
                            enhanced_layer.generate_intelligent_title(
                                thread_info["estimated_first_message"]
                            )
                        )
                        
                        # 创建会话元数据（使用原始 thread_id）
                        session = loop.run_until_complete(
                            enhanced_layer.create_session(
                                user_id=default_user["id"],
                                title=title,
                                metadata={
                                    "original_thread_id": thread_info["thread_id"],
                                    "checkpoint_count": thread_info["checkpoint_count"],
                                    "migrated_at": datetime.now().isoformat(),
                                    "migration_source": "langgraph_checkpoints"
                                }
                            )
                        )
                        
                        # 手动设置会话ID为原始thread_id以保持兼容性
                        conn = sqlite3.connect(enhanced_layer.db_path)
                        cursor = conn.cursor()
                        cursor.execute("""
                            UPDATE session_metadata 
                            SET id = ? 
                            WHERE id = ?
                        """, (thread_info["thread_id"], session["id"]))
                        conn.commit()
                        conn.close()
                        
                        migrated_sessions.append({
                            "original_id": session["id"],
                            "new_id": thread_info["thread_id"],
                            "title": title
                        })
                        
                    except Exception as e:
                        logger.error(f"迁移线程 {thread_info['thread_id']} 失败: {e}")
                        continue
                
                logger.info(f"成功迁移 {len(migrated_sessions)} 个会话")
                
                # 创建迁移报告
                migration_report = {
                    "migration_date": datetime.now().isoformat(),
                    "original_database": str(self.existing_db_path),
                    "enhanced_database": str(self.enhanced_db_path),
                    "original_stats": existing_analysis,
                    "migrated_sessions": len(migrated_sessions),
                    "default_user": default_user,
                    "sessions": migrated_sessions[:10]  # 只保存前10个作为示例
                }
                
                # 保存迁移报告
                report_path = self.enhanced_db_path.parent / "migration_report.json"
                with open(report_path, 'w', encoding='utf-8') as f:
                    json.dump(migration_report, f, ensure_ascii=False, indent=2)
                
                logger.info(f"迁移完成！报告保存到: {report_path}")
                return True
                
            finally:
                if 'loop' in locals():
                    loop.close()
                
        except Exception as e:
            logger.error(f"数据库迁移失败: {e}")
            return False
    
    def verify_migration(self) -> dict:
        """验证迁移结果"""
        try:
            from services.enhanced_data_layer import EnhancedDataLayer
            enhanced_layer = EnhancedDataLayer(str(self.enhanced_db_path))
            
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                stats = loop.run_until_complete(enhanced_layer.get_database_stats())
                return {
                    "verification_success": True,
                    "enhanced_database_stats": stats,
                    "database_path": str(self.enhanced_db_path)
                }
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"验证迁移失败: {e}")
            return {
                "verification_success": False,
                "error": str(e)
            }


def run_migration():
    """运行数据库迁移的主函数"""
    migration = DatabaseMigration()
    
    logger.info("开始数据库迁移...")
    
    # 检查现有数据库
    if migration.check_existing_database():
        logger.info(f"发现现有数据库: {migration.existing_db_path}")
        existing_tables = migration.get_existing_tables()
        logger.info(f"现有表: {existing_tables}")
    else:
        logger.info("未发现现有数据库，将创建全新的增强数据库")
    
    # 执行迁移
    success = migration.migrate_to_enhanced_schema()
    
    if success:
        # 验证迁移结果
        verification = migration.verify_migration()
        logger.info(f"迁移验证结果: {verification}")
        return True
    else:
        logger.error("数据库迁移失败")
        return False


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    run_migration()
