"""
简化的数据库迁移脚本 - 避免事件循环冲突
"""

import sqlite3
import json
from datetime import datetime
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


def simple_migrate_to_enhanced_schema(
    existing_db_path: str = "./data/agent_memory.db",
    enhanced_db_path: str = "./data/enhanced_sessions.db"
) -> bool:
    """
    简化的迁移函数，避免异步事件循环冲突
    """
    try:
        # 创建增强数据层实例
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

        from services.enhanced_data_layer import EnhancedDataLayer
        enhanced_layer = EnhancedDataLayer(enhanced_db_path)
        
        # 使用直接的数据库操作而不是异步方法
        conn = sqlite3.connect(enhanced_layer.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建默认用户
            user_id = "default_user_id"
            cursor.execute("""
                INSERT OR IGNORE INTO users (id, identifier, display_name, metadata, created_at)
                VALUES (?, ?, ?, ?, ?)
            """, (
                user_id,
                "default_user",
                "默认用户",
                json.dumps({"migrated_from": "langgraph_checkpoints"}, ensure_ascii=False),
                datetime.now().isoformat()
            ))
            
            # 检查现有数据库
            existing_path = Path(existing_db_path)
            if existing_path.exists():
                # 连接现有数据库
                existing_conn = sqlite3.connect(existing_path)
                existing_cursor = existing_conn.cursor()
                
                try:
                    # 检查是否有 checkpoints 表
                    existing_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='checkpoints'")
                    if existing_cursor.fetchone():
                        # 获取线程信息
                        existing_cursor.execute("""
                            SELECT DISTINCT thread_id FROM checkpoints
                            LIMIT 10
                        """)
                        
                        threads = existing_cursor.fetchall()
                        logger.info(f"发现 {len(threads)} 个历史线程")
                        
                        # 为每个线程创建会话
                        for thread_row in threads:
                            thread_id = thread_row[0]
                            
                            # 生成智能标题
                            title = f"历史对话 {thread_id[:8]}..."
                            
                            # 创建会话元数据
                            cursor.execute("""
                                INSERT OR IGNORE INTO session_metadata 
                                (id, user_id, title, created_at, updated_at, last_activity, metadata)
                                VALUES (?, ?, ?, ?, ?, ?, ?)
                            """, (
                                thread_id,  # 使用原始 thread_id 作为会话 ID
                                user_id,
                                title,
                                datetime.now().isoformat(),
                                datetime.now().isoformat(),
                                datetime.now().isoformat(),
                                json.dumps({
                                    "original_thread_id": thread_id,
                                    "migrated_at": datetime.now().isoformat(),
                                    "migration_source": "langgraph_checkpoints"
                                }, ensure_ascii=False)
                            ))
                        
                        logger.info(f"成功迁移 {len(threads)} 个会话")
                    
                finally:
                    existing_conn.close()
            
            conn.commit()
            
            # 验证迁移结果
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM session_metadata")
            session_count = cursor.fetchone()[0]
            
            logger.info(f"迁移完成 - 用户: {user_count}, 会话: {session_count}")
            
            return True
            
        finally:
            conn.close()
            
    except Exception as e:
        logger.error(f"简化迁移失败: {e}")
        return False


def test_simple_migration():
    """测试简化迁移功能"""
    import tempfile
    import shutil
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        existing_db = Path(temp_dir) / "existing.db"
        enhanced_db = Path(temp_dir) / "enhanced.db"
        
        # 创建模拟的现有数据库
        conn = sqlite3.connect(existing_db)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE checkpoints (
                thread_id TEXT,
                checkpoint_id INTEGER,
                checkpoint BLOB
            )
        """)
        cursor.execute("INSERT INTO checkpoints VALUES (?, ?, ?)", 
                      ("test_thread_1", 1, b"test_data"))
        cursor.execute("INSERT INTO checkpoints VALUES (?, ?, ?)", 
                      ("test_thread_2", 1, b"test_data"))
        conn.commit()
        conn.close()
        
        # 执行迁移
        success = simple_migrate_to_enhanced_schema(str(existing_db), str(enhanced_db))
        
        if success:
            # 验证结果
            conn = sqlite3.connect(enhanced_db)
            cursor = conn.cursor()
            
            cursor.execute("SELECT COUNT(*) FROM users")
            user_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM session_metadata")
            session_count = cursor.fetchone()[0]
            
            conn.close()
            
            print(f"✅ 简化迁移测试成功 - 用户: {user_count}, 会话: {session_count}")
            return True
        else:
            print("❌ 简化迁移测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 简化迁移测试异常: {e}")
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_simple_migration()
