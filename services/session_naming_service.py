"""
智能会话命名服务 - 借鉴 LangGraphChainlitAgent 的智能命名逻辑
基于用户首条消息自动生成有意义的会话标题
"""

import re
import asyncio
from datetime import datetime
from typing import Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)


class SessionNamingService:
    """
    智能会话命名服务
    借鉴 LangGraphChainlitAgent 的 generate_thread_name 函数设计
    """
    
    def __init__(self, max_title_length: int = 30, min_content_length: int = 5):
        self.max_title_length = max_title_length
        self.min_content_length = min_content_length
    
    async def generate_intelligent_title(self, first_message: str, 
                                       fallback_format: str = "对话 {time}") -> str:
        """
        基于用户的第一条消息生成智能会话名称
        完全借鉴 LangGraphChainlitAgent 的实现逻辑
        
        Args:
            first_message: 用户的第一条消息内容
            fallback_format: 当消息太短时的后备格式
            
        Returns:
            生成的智能标题
        """
        # 清理消息内容
        content = first_message.strip()
        
        # 如果消息太短，使用默认格式
        if len(content) < self.min_content_length:
            current_time = datetime.now().strftime('%m-%d %H:%M')
            return fallback_format.format(time=current_time)
        
        # 截取前30个字符作为标题，确保不会太长
        if len(content) > self.max_title_length:
            title = content[:self.max_title_length - 3] + "..."
        else:
            title = content
        
        # 移除换行符和多余空格
        title = " ".join(title.split())
        
        # 如果原消息是问号结尾，保留问号
        if content.endswith('?') and not title.endswith('?'):
            title = title.rstrip('.') + '?'
        
        return title
    
    async def generate_title_with_context(self, first_message: str, 
                                        user_info: Optional[Dict[str, Any]] = None,
                                        session_context: Optional[Dict[str, Any]] = None) -> str:
        """
        基于上下文信息生成更智能的标题
        
        Args:
            first_message: 用户的第一条消息
            user_info: 用户信息（可选）
            session_context: 会话上下文信息（可选）
            
        Returns:
            生成的智能标题
        """
        # 首先使用基础的智能命名
        base_title = await self.generate_intelligent_title(first_message)
        
        # 如果有用户信息，可以进行个性化调整
        if user_info and user_info.get('display_name'):
            # 这里可以根据用户偏好调整标题格式
            # 目前保持简单，直接返回基础标题
            pass
        
        # 如果有会话上下文，可以进行进一步优化
        if session_context:
            # 例如：检测特定的对话类型（编程、翻译、创作等）
            title_type = self._detect_conversation_type(first_message)
            if title_type:
                # 可以在标题前添加类型标识，但要保持在长度限制内
                type_prefix = self._get_type_prefix(title_type)
                if type_prefix and len(base_title) + len(type_prefix) <= self.max_title_length:
                    base_title = f"{type_prefix}{base_title}"
        
        return base_title
    
    def _detect_conversation_type(self, message: str) -> Optional[str]:
        """
        检测对话类型
        
        Args:
            message: 用户消息
            
        Returns:
            检测到的对话类型
        """
        message_lower = message.lower()
        
        # 编程相关
        if any(keyword in message_lower for keyword in [
            'code', '代码', 'python', 'javascript', 'java', 'c++', 'html', 'css',
            '编程', '程序', '函数', '算法', 'bug', '调试', 'debug'
        ]):
            return 'programming'
        
        # 翻译相关
        if any(keyword in message_lower for keyword in [
            'translate', '翻译', 'english', '英文', '中文', 'chinese'
        ]):
            return 'translation'
        
        # 创作相关
        if any(keyword in message_lower for keyword in [
            '写', '创作', '文章', '故事', '诗歌', '小说', 'write', 'create'
        ]):
            return 'writing'
        
        # 学习相关
        if any(keyword in message_lower for keyword in [
            '学习', '教', '解释', '什么是', 'learn', 'explain', 'how to'
        ]):
            return 'learning'
        
        return None
    
    def _get_type_prefix(self, conversation_type: str) -> str:
        """
        获取对话类型的前缀标识
        
        Args:
            conversation_type: 对话类型
            
        Returns:
            类型前缀
        """
        prefixes = {
            'programming': '💻 ',
            'translation': '🌐 ',
            'writing': '✍️ ',
            'learning': '📚 '
        }
        return prefixes.get(conversation_type, '')
    
    async def update_title_if_needed(self, data_layer, session_id: str, 
                                   first_message: str, 
                                   user_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        检查并更新会话标题（仅在第一条消息后）
        借鉴 LangGraphChainlitAgent 的 update_thread_name_if_needed 逻辑
        
        Args:
            data_layer: 数据层实例
            session_id: 会话ID
            first_message: 第一条消息内容
            user_info: 用户信息（可选）
            
        Returns:
            是否成功更新标题
        """
        try:
            # 获取会话信息
            session = await data_layer.get_session(session_id)
            if not session:
                logger.warning(f"会话不存在: {session_id}")
                return False
            
            # 如果已经有标题，跳过更新
            if session.get('title'):
                logger.info(f"🏷️ 会话已有标题，跳过更新: {session['title']}")
                return False
            
            # 生成智能标题
            new_title = await self.generate_title_with_context(
                first_message, 
                user_info=user_info,
                session_context=session.get('metadata', {})
            )
            
            # 更新会话标题
            success = await data_layer.update_session(session_id, title=new_title)
            if success:
                logger.info(f"✅ 会话标题已更新: {session_id} -> '{new_title}'")
            else:
                logger.error(f"❌ 更新会话标题失败: {session_id}")
            
            return success
            
        except Exception as e:
            logger.error(f"❌ 更新会话标题时发生错误: {str(e)}")
            return False
    
    async def batch_update_titles(self, data_layer, sessions_data: list) -> Dict[str, Any]:
        """
        批量更新会话标题
        
        Args:
            data_layer: 数据层实例
            sessions_data: 会话数据列表，每个元素包含 session_id 和 first_message
            
        Returns:
            批量更新结果统计
        """
        results = {
            'total': len(sessions_data),
            'updated': 0,
            'skipped': 0,
            'failed': 0,
            'details': []
        }
        
        for session_data in sessions_data:
            session_id = session_data.get('session_id')
            first_message = session_data.get('first_message', '')
            user_info = session_data.get('user_info')
            
            try:
                success = await self.update_title_if_needed(
                    data_layer, session_id, first_message, user_info
                )
                
                if success:
                    results['updated'] += 1
                    status = 'updated'
                else:
                    results['skipped'] += 1
                    status = 'skipped'
                
                results['details'].append({
                    'session_id': session_id,
                    'status': status
                })
                
            except Exception as e:
                results['failed'] += 1
                results['details'].append({
                    'session_id': session_id,
                    'status': 'failed',
                    'error': str(e)
                })
                logger.error(f"批量更新标题失败 - 会话 {session_id}: {e}")
        
        logger.info(f"批量标题更新完成: {results}")
        return results
    
    def validate_title(self, title: str) -> bool:
        """
        验证标题是否符合规范
        
        Args:
            title: 要验证的标题
            
        Returns:
            是否有效
        """
        if not title or not title.strip():
            return False
        
        # 检查长度
        if len(title) > self.max_title_length:
            return False
        
        # 检查是否包含不当字符
        invalid_chars = ['\n', '\r', '\t']
        if any(char in title for char in invalid_chars):
            return False
        
        return True
    
    async def suggest_alternative_titles(self, original_message: str, 
                                       count: int = 3) -> list:
        """
        为同一条消息生成多个候选标题
        
        Args:
            original_message: 原始消息
            count: 生成候选标题的数量
            
        Returns:
            候选标题列表
        """
        alternatives = []
        
        # 基础标题
        base_title = await self.generate_intelligent_title(original_message)
        alternatives.append(base_title)
        
        if count > 1:
            # 更短的版本
            if len(original_message) > 20:
                short_title = await self.generate_intelligent_title(
                    original_message, fallback_format="简短对话 {time}"
                )
                # 手动创建更短的版本
                content = original_message.strip()
                if len(content) > 15:
                    short_title = content[:12] + "..."
                    short_title = " ".join(short_title.split())
                    if content.endswith('?') and not short_title.endswith('?'):
                        short_title = short_title.rstrip('.') + '?'
                alternatives.append(short_title)
        
        if count > 2:
            # 带时间戳的版本
            time_title = f"{base_title} ({datetime.now().strftime('%H:%M')})"
            if len(time_title) <= self.max_title_length:
                alternatives.append(time_title)
            else:
                # 如果太长，使用简化版本
                simple_time = datetime.now().strftime('%m-%d')
                time_title = f"对话 {simple_time}"
                alternatives.append(time_title)
        
        # 去重并限制数量
        unique_alternatives = []
        for title in alternatives:
            if title not in unique_alternatives and self.validate_title(title):
                unique_alternatives.append(title)
        
        return unique_alternatives[:count]


# 创建全局实例
session_naming_service = SessionNamingService()


# 便捷函数
async def generate_session_title(first_message: str) -> str:
    """便捷函数：生成会话标题"""
    return await session_naming_service.generate_intelligent_title(first_message)


async def update_session_title_if_needed(data_layer, session_id: str, 
                                       first_message: str, 
                                       user_info: Optional[Dict[str, Any]] = None) -> bool:
    """便捷函数：更新会话标题"""
    return await session_naming_service.update_title_if_needed(
        data_layer, session_id, first_message, user_info
    )
