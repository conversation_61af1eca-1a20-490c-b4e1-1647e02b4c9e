"""
数据库连接和事务管理优化
提供连接池、事务装饰器和数据一致性保证
"""

import sqlite3
import asyncio
import threading
import contextlib
from typing import Optional, Any, Callable, Dict
from functools import wraps
from pathlib import Path
import logging
import time

logger = logging.getLogger(__name__)


class DatabaseConnectionPool:
    """SQLite 连接池管理器"""
    
    def __init__(self, db_path: str, max_connections: int = 10, 
                 timeout: float = 30.0, check_same_thread: bool = False):
        self.db_path = Path(db_path)
        self.max_connections = max_connections
        self.timeout = timeout
        self.check_same_thread = check_same_thread
        
        # 连接池
        self._pool = []
        self._pool_lock = threading.Lock()
        self._active_connections = 0
        
        # 统计信息
        self._stats = {
            'total_connections_created': 0,
            'total_connections_reused': 0,
            'current_active': 0,
            'peak_active': 0
        }
        
        # 确保数据库目录存在
        self.db_path.parent.mkdir(parents=True, exist_ok=True)
    
    def _create_connection(self) -> sqlite3.Connection:
        """创建新的数据库连接"""
        conn = sqlite3.connect(
            self.db_path,
            timeout=self.timeout,
            check_same_thread=self.check_same_thread
        )
        
        # 优化设置
        conn.execute("PRAGMA journal_mode=WAL")  # 启用 WAL 模式提高并发性能
        conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
        conn.execute("PRAGMA cache_size=10000")  # 增加缓存大小
        conn.execute("PRAGMA temp_store=MEMORY")  # 临时表存储在内存中
        conn.execute("PRAGMA mmap_size=268435456")  # 启用内存映射 (256MB)
        
        # 启用外键约束
        conn.execute("PRAGMA foreign_keys=ON")
        
        self._stats['total_connections_created'] += 1
        return conn
    
    @contextlib.contextmanager
    def get_connection(self):
        """获取数据库连接的上下文管理器"""
        conn = None
        try:
            # 尝试从池中获取连接
            with self._pool_lock:
                if self._pool:
                    conn = self._pool.pop()
                    self._stats['total_connections_reused'] += 1
                elif self._active_connections < self.max_connections:
                    conn = self._create_connection()
                    self._active_connections += 1
                    self._stats['current_active'] = self._active_connections
                    if self._active_connections > self._stats['peak_active']:
                        self._stats['peak_active'] = self._active_connections
            
            if conn is None:
                raise Exception(f"无法获取数据库连接，已达到最大连接数: {self.max_connections}")
            
            yield conn
            
        except Exception as e:
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise e
        finally:
            if conn:
                try:
                    # 将连接返回池中
                    with self._pool_lock:
                        if len(self._pool) < self.max_connections // 2:  # 保持池中一定数量的连接
                            self._pool.append(conn)
                        else:
                            conn.close()
                            self._active_connections -= 1
                            self._stats['current_active'] = self._active_connections
                except Exception as e:
                    logger.error(f"返回连接到池时出错: {e}")
    
    def close_all(self):
        """关闭所有连接"""
        with self._pool_lock:
            for conn in self._pool:
                try:
                    conn.close()
                except:
                    pass
            self._pool.clear()
            self._active_connections = 0
            self._stats['current_active'] = 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计信息"""
        with self._pool_lock:
            return {
                **self._stats,
                'pool_size': len(self._pool),
                'max_connections': self.max_connections
            }


class TransactionManager:
    """事务管理器"""
    
    def __init__(self, connection_pool: DatabaseConnectionPool):
        self.connection_pool = connection_pool
    
    @contextlib.contextmanager
    def transaction(self, isolation_level: Optional[str] = None):
        """事务上下文管理器"""
        with self.connection_pool.get_connection() as conn:
            if isolation_level:
                conn.isolation_level = isolation_level
            
            try:
                conn.execute("BEGIN")
                yield conn
                conn.commit()
            except Exception as e:
                conn.rollback()
                logger.error(f"事务回滚: {e}")
                raise
            finally:
                # 恢复默认隔离级别
                if isolation_level:
                    conn.isolation_level = None


def with_transaction(isolation_level: Optional[str] = None, 
                    retry_count: int = 3, 
                    retry_delay: float = 0.1):
    """
    事务装饰器
    
    Args:
        isolation_level: 事务隔离级别
        retry_count: 重试次数
        retry_delay: 重试延迟（秒）
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            if not hasattr(self, '_transaction_manager'):
                raise AttributeError("对象必须有 _transaction_manager 属性")
            
            last_exception = None
            for attempt in range(retry_count + 1):
                try:
                    def _execute():
                        with self._transaction_manager.transaction(isolation_level) as conn:
                            return func(self, conn, *args, **kwargs)
                    
                    if asyncio.iscoroutinefunction(func):
                        # 异步函数
                        return await asyncio.get_event_loop().run_in_executor(None, _execute)
                    else:
                        # 同步函数
                        return await asyncio.get_event_loop().run_in_executor(None, _execute)
                        
                except sqlite3.OperationalError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < retry_count:
                        logger.warning(f"数据库锁定，重试 {attempt + 1}/{retry_count}: {e}")
                        await asyncio.sleep(retry_delay * (2 ** attempt))  # 指数退避
                        continue
                    raise
                except Exception as e:
                    logger.error(f"事务执行失败: {e}")
                    raise
            
            if last_exception:
                raise last_exception
        
        @wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            if not hasattr(self, '_transaction_manager'):
                raise AttributeError("对象必须有 _transaction_manager 属性")
            
            last_exception = None
            for attempt in range(retry_count + 1):
                try:
                    with self._transaction_manager.transaction(isolation_level) as conn:
                        return func(self, conn, *args, **kwargs)
                        
                except sqlite3.OperationalError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < retry_count:
                        logger.warning(f"数据库锁定，重试 {attempt + 1}/{retry_count}: {e}")
                        time.sleep(retry_delay * (2 ** attempt))  # 指数退避
                        continue
                    raise
                except Exception as e:
                    logger.error(f"事务执行失败: {e}")
                    raise
            
            if last_exception:
                raise last_exception
        
        # 根据函数类型返回相应的包装器
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


def with_connection(retry_count: int = 3, retry_delay: float = 0.1):
    """
    连接装饰器（不使用事务）
    
    Args:
        retry_count: 重试次数
        retry_delay: 重试延迟（秒）
    """
    def decorator(func: Callable):
        @wraps(func)
        async def async_wrapper(self, *args, **kwargs):
            if not hasattr(self, '_connection_pool'):
                raise AttributeError("对象必须有 _connection_pool 属性")
            
            last_exception = None
            for attempt in range(retry_count + 1):
                try:
                    def _execute():
                        with self._connection_pool.get_connection() as conn:
                            return func(self, conn, *args, **kwargs)
                    
                    if asyncio.iscoroutinefunction(func):
                        return await asyncio.get_event_loop().run_in_executor(None, _execute)
                    else:
                        return await asyncio.get_event_loop().run_in_executor(None, _execute)
                        
                except sqlite3.OperationalError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < retry_count:
                        logger.warning(f"数据库锁定，重试 {attempt + 1}/{retry_count}: {e}")
                        await asyncio.sleep(retry_delay * (2 ** attempt))
                        continue
                    raise
                except Exception as e:
                    logger.error(f"连接执行失败: {e}")
                    raise
            
            if last_exception:
                raise last_exception
        
        @wraps(func)
        def sync_wrapper(self, *args, **kwargs):
            if not hasattr(self, '_connection_pool'):
                raise AttributeError("对象必须有 _connection_pool 属性")
            
            last_exception = None
            for attempt in range(retry_count + 1):
                try:
                    with self._connection_pool.get_connection() as conn:
                        return func(self, conn, *args, **kwargs)
                        
                except sqlite3.OperationalError as e:
                    last_exception = e
                    if "database is locked" in str(e).lower() and attempt < retry_count:
                        logger.warning(f"数据库锁定，重试 {attempt + 1}/{retry_count}: {e}")
                        time.sleep(retry_delay * (2 ** attempt))
                        continue
                    raise
                except Exception as e:
                    logger.error(f"连接执行失败: {e}")
                    raise
            
            if last_exception:
                raise last_exception
        
        if asyncio.iscoroutinefunction(func):
            return async_wrapper
        else:
            return sync_wrapper
    
    return decorator


class DatabaseHealthChecker:
    """数据库健康检查器"""
    
    def __init__(self, connection_pool: DatabaseConnectionPool):
        self.connection_pool = connection_pool
    
    async def check_health(self) -> Dict[str, Any]:
        """检查数据库健康状态"""
        health_info = {
            'status': 'unknown',
            'connection_pool_stats': {},
            'database_accessible': False,
            'wal_mode_enabled': False,
            'foreign_keys_enabled': False,
            'response_time_ms': 0,
            'error': None
        }
        
        try:
            start_time = time.time()
            
            def _check():
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 基本连接测试
                    cursor.execute("SELECT 1")
                    result = cursor.fetchone()
                    if result[0] != 1:
                        raise Exception("基本查询失败")
                    
                    # 检查 WAL 模式
                    cursor.execute("PRAGMA journal_mode")
                    journal_mode = cursor.fetchone()[0]
                    health_info['wal_mode_enabled'] = journal_mode.upper() == 'WAL'
                    
                    # 检查外键约束
                    cursor.execute("PRAGMA foreign_keys")
                    foreign_keys = cursor.fetchone()[0]
                    health_info['foreign_keys_enabled'] = bool(foreign_keys)
                    
                    return True
            
            await asyncio.get_event_loop().run_in_executor(None, _check)
            
            health_info['database_accessible'] = True
            health_info['response_time_ms'] = round((time.time() - start_time) * 1000, 2)
            health_info['connection_pool_stats'] = self.connection_pool.get_stats()
            health_info['status'] = 'healthy'
            
        except Exception as e:
            health_info['status'] = 'unhealthy'
            health_info['error'] = str(e)
            logger.error(f"数据库健康检查失败: {e}")
        
        return health_info
    
    async def perform_maintenance(self) -> Dict[str, Any]:
        """执行数据库维护操作"""
        maintenance_info = {
            'vacuum_completed': False,
            'analyze_completed': False,
            'integrity_check_passed': False,
            'maintenance_time_ms': 0,
            'errors': []
        }
        
        start_time = time.time()
        
        try:
            def _maintenance():
                with self.connection_pool.get_connection() as conn:
                    cursor = conn.cursor()
                    
                    # 完整性检查
                    cursor.execute("PRAGMA integrity_check")
                    integrity_result = cursor.fetchone()[0]
                    maintenance_info['integrity_check_passed'] = integrity_result == 'ok'
                    
                    # 分析统计信息
                    cursor.execute("ANALYZE")
                    maintenance_info['analyze_completed'] = True
                    
                    # 清理数据库
                    cursor.execute("VACUUM")
                    maintenance_info['vacuum_completed'] = True
            
            await asyncio.get_event_loop().run_in_executor(None, _maintenance)
            
        except Exception as e:
            maintenance_info['errors'].append(str(e))
            logger.error(f"数据库维护失败: {e}")
        
        maintenance_info['maintenance_time_ms'] = round((time.time() - start_time) * 1000, 2)
        return maintenance_info
