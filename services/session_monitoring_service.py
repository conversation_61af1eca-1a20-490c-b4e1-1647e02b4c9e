"""
会话状态监控服务
提供会话统计、用户活跃度分析、性能监控等功能
"""

import sqlite3
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from services.enhanced_data_layer import EnhancedDataLayer


class SessionMonitoringService:
    """会话状态监控服务"""
    
    def __init__(self, data_layer: EnhancedDataLayer):
        """初始化监控服务"""
        self.data_layer = data_layer
        # 使用数据层的数据库路径
        self.db_path = data_layer.db_path
    
    async def get_active_sessions_count(self, time_window_hours: int = 24) -> Dict[str, Any]:
        """获取活跃会话数量统计"""
        try:
            # 计算时间窗口
            cutoff_time = datetime.now() - timedelta(hours=time_window_hours)
            cutoff_timestamp = cutoff_time.isoformat()
            
            # 查询活跃会话
            query = """
            SELECT COUNT(DISTINCT s.id) as active_sessions,
                   COUNT(DISTINCT s.user_id) as active_users
            FROM session_metadata s
            JOIN messages m ON s.id = m.session_id
            WHERE m.created_at >= ?
            """
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, (cutoff_timestamp,))
                result = cursor.fetchone()
                
                active_sessions, active_users = result if result else (0, 0)
            
            # 获取总会话数
            total_query = "SELECT COUNT(*) FROM session_metadata"
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(total_query)
                total_sessions = cursor.fetchone()[0]
            
            return {
                "time_window_hours": time_window_hours,
                "active_sessions": active_sessions,
                "active_users": active_users,
                "total_sessions": total_sessions,
                "activity_rate": round(active_sessions / max(total_sessions, 1) * 100, 2),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"获取活跃会话统计失败: {e}",
                "active_sessions": 0,
                "active_users": 0,
                "total_sessions": 0
            }
    
    async def get_session_duration_analysis(self, limit: int = 100) -> Dict[str, Any]:
        """分析会话持续时间"""
        try:
            query = """
            SELECT s.id, s.title, s.created_at,
                   MIN(m.created_at) as first_message,
                   MAX(m.created_at) as last_message,
                   COUNT(m.id) as message_count
            FROM session_metadata s
            LEFT JOIN messages m ON s.id = m.session_id
            GROUP BY s.id
            ORDER BY s.created_at DESC
            LIMIT ?
            """
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, (limit,))
                results = cursor.fetchall()
            
            durations = []
            total_duration = 0
            active_sessions = 0
            
            for row in results:
                session_id, title, created_at, first_msg, last_msg, msg_count = row
                
                if first_msg and last_msg:
                    # 计算会话持续时间
                    first_time = datetime.fromisoformat(first_msg)
                    last_time = datetime.fromisoformat(last_msg)
                    duration_minutes = (last_time - first_time).total_seconds() / 60
                    
                    durations.append({
                        "session_id": session_id,
                        "title": title[:50] + "..." if len(title) > 50 else title,
                        "duration_minutes": round(duration_minutes, 2),
                        "message_count": msg_count,
                        "messages_per_minute": round(msg_count / max(duration_minutes, 1), 2)
                    })
                    
                    total_duration += duration_minutes
                    active_sessions += 1
            
            # 计算统计信息
            avg_duration = total_duration / max(active_sessions, 1)
            durations.sort(key=lambda x: x["duration_minutes"], reverse=True)
            
            return {
                "total_analyzed_sessions": len(results),
                "active_sessions_with_messages": active_sessions,
                "average_duration_minutes": round(avg_duration, 2),
                "longest_sessions": durations[:10],  # 前10个最长会话
                "total_duration_hours": round(total_duration / 60, 2),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"会话持续时间分析失败: {e}",
                "total_analyzed_sessions": 0,
                "active_sessions_with_messages": 0
            }
    
    async def get_message_frequency_stats(self, days: int = 7) -> Dict[str, Any]:
        """获取消息频率统计"""
        try:
            # 计算时间范围
            start_date = datetime.now() - timedelta(days=days)
            start_timestamp = start_date.isoformat()
            
            # 按日期统计消息
            daily_query = """
            SELECT DATE(created_at) as date,
                   COUNT(*) as message_count,
                   COUNT(DISTINCT session_id) as active_sessions
            FROM messages
            WHERE created_at >= ?
            GROUP BY DATE(created_at)
            ORDER BY date DESC
            """

            # 按小时统计消息（最近24小时）
            hourly_query = """
            SELECT strftime('%H', created_at) as hour,
                   COUNT(*) as message_count
            FROM messages
            WHERE created_at >= datetime('now', '-24 hours')
            GROUP BY strftime('%H', created_at)
            ORDER BY hour
            """
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 日统计
                cursor.execute(daily_query, (start_timestamp,))
                daily_results = cursor.fetchall()
                
                # 小时统计
                cursor.execute(hourly_query)
                hourly_results = cursor.fetchall()
            
            # 处理日统计
            daily_stats = []
            total_messages = 0
            for date, msg_count, sessions in daily_results:
                daily_stats.append({
                    "date": date,
                    "message_count": msg_count,
                    "active_sessions": sessions,
                    "avg_messages_per_session": round(msg_count / max(sessions, 1), 2)
                })
                total_messages += msg_count
            
            # 处理小时统计
            hourly_stats = []
            for hour, msg_count in hourly_results:
                hourly_stats.append({
                    "hour": f"{hour}:00",
                    "message_count": msg_count
                })
            
            # 计算平均值
            avg_daily_messages = total_messages / max(len(daily_stats), 1)
            
            return {
                "analysis_period_days": days,
                "total_messages": total_messages,
                "average_daily_messages": round(avg_daily_messages, 2),
                "daily_statistics": daily_stats,
                "hourly_statistics_24h": hourly_stats,
                "peak_hour": max(hourly_stats, key=lambda x: x["message_count"])["hour"] if hourly_stats else "N/A",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"消息频率统计失败: {e}",
                "total_messages": 0,
                "daily_statistics": [],
                "hourly_statistics_24h": []
            }
    
    async def get_user_activity_analysis(self, limit: int = 50) -> Dict[str, Any]:
        """分析用户活跃度"""
        try:
            query = """
            SELECT u.identifier, u.display_name,
                   COUNT(DISTINCT s.id) as session_count,
                   COUNT(m.id) as message_count,
                   MAX(m.created_at) as last_activity,
                   MIN(m.created_at) as first_activity
            FROM users u
            LEFT JOIN session_metadata s ON u.id = s.user_id
            LEFT JOIN messages m ON s.id = m.session_id
            WHERE m.role = 'user'  -- 只统计用户发送的消息
            GROUP BY u.id
            ORDER BY message_count DESC
            LIMIT ?
            """
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                cursor.execute(query, (limit,))
                results = cursor.fetchall()
            
            user_stats = []
            total_users = 0
            total_sessions = 0
            total_messages = 0
            
            for row in results:
                identifier, display_name, sessions, messages, last_act, first_act = row
                
                # 计算活跃天数
                active_days = 1
                if first_act and last_act:
                    first_time = datetime.fromisoformat(first_act)
                    last_time = datetime.fromisoformat(last_act)
                    active_days = max((last_time - first_time).days + 1, 1)
                
                user_stats.append({
                    "user_identifier": identifier,
                    "display_name": display_name or "未知用户",
                    "session_count": sessions,
                    "message_count": messages,
                    "last_activity": last_act,
                    "active_days": active_days,
                    "avg_messages_per_day": round(messages / active_days, 2),
                    "avg_messages_per_session": round(messages / max(sessions, 1), 2)
                })
                
                total_users += 1
                total_sessions += sessions
                total_messages += messages
            
            # 计算总体统计
            avg_sessions_per_user = total_sessions / max(total_users, 1)
            avg_messages_per_user = total_messages / max(total_users, 1)
            
            return {
                "total_analyzed_users": total_users,
                "total_sessions": total_sessions,
                "total_user_messages": total_messages,
                "average_sessions_per_user": round(avg_sessions_per_user, 2),
                "average_messages_per_user": round(avg_messages_per_user, 2),
                "top_users": user_stats[:20],  # 前20个最活跃用户
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"用户活跃度分析失败: {e}",
                "total_analyzed_users": 0,
                "top_users": []
            }
    
    async def get_system_health_metrics(self) -> Dict[str, Any]:
        """获取系统健康指标"""
        try:
            # 数据库大小和表统计
            db_stats_query = """
            SELECT name,
                   (SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name=main.name) as table_exists
            FROM (
                SELECT 'session_metadata' as name
                UNION SELECT 'messages' as name
                UNION SELECT 'users' as name
            ) main
            """
            
            # 最近活动统计
            recent_activity_query = """
            SELECT
                (SELECT COUNT(*) FROM session_metadata WHERE created_at >= datetime('now', '-1 hour')) as sessions_last_hour,
                (SELECT COUNT(*) FROM messages WHERE created_at >= datetime('now', '-1 hour')) as messages_last_hour,
                (SELECT COUNT(*) FROM session_metadata WHERE created_at >= datetime('now', '-24 hours')) as sessions_last_24h,
                (SELECT COUNT(*) FROM messages WHERE created_at >= datetime('now', '-24 hours')) as messages_last_24h
            """
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # 获取数据库文件大小
                import os
                db_size_mb = os.path.getsize(self.db_path) / (1024 * 1024) if os.path.exists(self.db_path) else 0
                
                # 获取表统计
                cursor.execute(db_stats_query)
                table_stats = cursor.fetchall()
                
                # 获取最近活动
                cursor.execute(recent_activity_query)
                activity_result = cursor.fetchone()
                
                sessions_1h, messages_1h, sessions_24h, messages_24h = activity_result if activity_result else (0, 0, 0, 0)
            
            return {
                "database_size_mb": round(db_size_mb, 2),
                "tables_status": {name: bool(exists) for name, exists in table_stats},
                "recent_activity": {
                    "sessions_last_hour": sessions_1h,
                    "messages_last_hour": messages_1h,
                    "sessions_last_24h": sessions_24h,
                    "messages_last_24h": messages_24h
                },
                "system_status": "healthy" if db_size_mb > 0 else "warning",
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            return {
                "error": f"系统健康指标获取失败: {e}",
                "system_status": "error",
                "timestamp": datetime.now().isoformat()
            }


# 全局监控服务实例
_monitoring_service = None

def get_monitoring_service() -> SessionMonitoringService:
    """获取全局监控服务实例"""
    global _monitoring_service
    if _monitoring_service is None:
        from services.enhanced_data_layer import EnhancedDataLayer
        data_layer = EnhancedDataLayer()
        _monitoring_service = SessionMonitoringService(data_layer)
    return _monitoring_service
