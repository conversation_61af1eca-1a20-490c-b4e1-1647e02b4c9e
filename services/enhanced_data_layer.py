"""
增强的数据层抽象 - 借鉴 LangGraphChainlitAgent 的 SQLiteDataLayer 设计
支持会话、用户、消息的统一管理，提供完整的 CRUD 操作
"""

import json
import sqlite3
import asyncio
import uuid
import re
from typing import Dict, List, Optional, Any, TypedDict
from datetime import datetime
from pathlib import Path
import logging

from .database_manager import DatabaseConnectionPool, TransactionManager, with_transaction, with_connection

# 配置日志
logger = logging.getLogger(__name__)

# 数据模型定义
class SessionDict(TypedDict):
    """会话数据模型"""
    id: str
    user_id: str
    title: Optional[str]
    created_at: str
    updated_at: str
    last_activity: str
    message_count: int
    is_active: bool
    metadata: Dict[str, Any]

class UserDict(TypedDict):
    """用户数据模型"""
    id: str
    identifier: str
    display_name: str
    metadata: Dict[str, Any]
    created_at: str

class MessageDict(TypedDict):
    """消息数据模型"""
    id: str
    session_id: str
    role: str  # 'user' or 'assistant'
    content: str
    created_at: str
    metadata: Dict[str, Any]


class EnhancedDataLayer:
    """增强的数据层抽象，借鉴 LangGraphChainlitAgent 的优秀设计"""

    def __init__(self, db_path: str = "./data/enhanced_sessions.db"):
        self.db_path = Path(db_path)
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

        # 初始化连接池和事务管理器
        self._connection_pool = DatabaseConnectionPool(str(self.db_path))
        self._transaction_manager = TransactionManager(self._connection_pool)

        self._init_database()
    
    def _init_database(self):
        """初始化数据库表结构"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        try:
            # 创建用户表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS users (
                    id TEXT PRIMARY KEY,
                    identifier TEXT NOT NULL UNIQUE,
                    display_name TEXT,
                    metadata TEXT NOT NULL DEFAULT '{}',
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # 创建会话元数据表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS session_metadata (
                    id TEXT PRIMARY KEY,
                    user_id TEXT NOT NULL,
                    title TEXT,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    last_activity TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    message_count INTEGER DEFAULT 0,
                    is_active BOOLEAN DEFAULT 1,
                    metadata TEXT NOT NULL DEFAULT '{}',
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                )
            """)
            
            # 创建消息表
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS messages (
                    id TEXT PRIMARY KEY,
                    session_id TEXT NOT NULL,
                    role TEXT NOT NULL CHECK (role IN ('user', 'assistant', 'system')),
                    content TEXT NOT NULL,
                    created_at TEXT NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    metadata TEXT NOT NULL DEFAULT '{}',
                    FOREIGN KEY (session_id) REFERENCES session_metadata(id) ON DELETE CASCADE
                )
            """)
            
            # 创建索引
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_session_user_id ON session_metadata(user_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_session_last_activity ON session_metadata(last_activity)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_session_id ON messages(session_id)")
            cursor.execute("CREATE INDEX IF NOT EXISTS idx_messages_created_at ON messages(created_at)")
            
            conn.commit()
            logger.info("✅ 数据库初始化完成")
            
        except Exception as e:
            logger.error(f"❌ 数据库初始化失败: {e}")
            raise
        finally:
            conn.close()
    
    def _serialize_data(self, data: Any) -> str:
        """序列化复杂数据为 JSON 字符串"""
        if data is None:
            return "{}"
        if isinstance(data, (list, dict)):
            return json.dumps(data, ensure_ascii=False)
        return str(data)
    
    def _deserialize_data(self, data: str) -> Any:
        """反序列化 JSON 字符串为 Python 对象"""
        if not data:
            return {}
        try:
            return json.loads(data)
        except (json.JSONDecodeError, TypeError):
            return {}
    
    async def generate_intelligent_title(self, first_message: str) -> str:
        """
        智能生成会话标题 - 借鉴 LangGraphChainlitAgent 的实现
        基于用户首条消息自动生成有意义的会话标题
        """
        # 清理消息内容
        content = first_message.strip()
        
        # 如果消息太短，使用默认格式
        if len(content) < 5:
            return f"对话 {datetime.now().strftime('%m-%d %H:%M')}"
        
        # 截取前30个字符作为标题，确保不会太长
        if len(content) > 30:
            title = content[:27] + "..."
        else:
            title = content
        
        # 移除换行符和多余空格
        title = " ".join(title.split())
        
        # 如果是问号结尾，保留问号
        if content.endswith('?') and not title.endswith('?'):
            title = title.rstrip('.') + '?'
        
        return title
    
    # 用户管理方法
    async def create_user(self, identifier: str, display_name: Optional[str] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> UserDict:
        """创建用户"""
        def _create_user():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                user_id = str(uuid.uuid4())
                created_at = datetime.now().isoformat()
                
                cursor.execute("""
                    INSERT INTO users (id, identifier, display_name, metadata, created_at)
                    VALUES (?, ?, ?, ?, ?)
                """, (
                    user_id,
                    identifier,
                    display_name or identifier,
                    self._serialize_data(metadata or {}),
                    created_at
                ))
                conn.commit()
                
                return UserDict(
                    id=user_id,
                    identifier=identifier,
                    display_name=display_name or identifier,
                    metadata=metadata or {},
                    created_at=created_at
                )
            except sqlite3.IntegrityError:
                # 用户已存在，返回现有用户
                return self._get_user_by_identifier_sync(cursor, identifier)
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _create_user)
    
    def _get_user_by_identifier_sync(self, cursor, identifier: str) -> Optional[UserDict]:
        """同步获取用户（内部使用）"""
        cursor.execute("SELECT * FROM users WHERE identifier = ?", (identifier,))
        row = cursor.fetchone()
        if row:
            return UserDict(
                id=row[0],
                identifier=row[1],
                display_name=row[2],
                metadata=self._deserialize_data(row[3]),
                created_at=row[4]
            )
        return None
    
    async def get_user_by_identifier(self, identifier: str) -> Optional[UserDict]:
        """根据标识符获取用户"""
        def _get_user():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                return self._get_user_by_identifier_sync(cursor, identifier)
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _get_user)
    
    async def get_user_by_id(self, user_id: str) -> Optional[UserDict]:
        """根据ID获取用户"""
        def _get_user():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT * FROM users WHERE id = ?", (user_id,))
                row = cursor.fetchone()
                if row:
                    return UserDict(
                        id=row[0],
                        identifier=row[1],
                        display_name=row[2],
                        metadata=self._deserialize_data(row[3]),
                        created_at=row[4]
                    )
                return None
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _get_user)
    
    # 会话管理方法
    async def create_session(self, user_id: str, title: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None) -> SessionDict:
        """创建会话"""
        def _create_session():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                session_id = str(uuid.uuid4())
                now = datetime.now().isoformat()
                
                cursor.execute("""
                    INSERT INTO session_metadata 
                    (id, user_id, title, created_at, updated_at, last_activity, metadata)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (
                    session_id,
                    user_id,
                    title,
                    now,
                    now,
                    now,
                    self._serialize_data(metadata or {})
                ))
                conn.commit()
                
                return SessionDict(
                    id=session_id,
                    user_id=user_id,
                    title=title,
                    created_at=now,
                    updated_at=now,
                    last_activity=now,
                    message_count=0,
                    is_active=True,
                    metadata=metadata or {}
                )
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _create_session)
    
    async def get_session(self, session_id: str) -> Optional[SessionDict]:
        """获取会话"""
        def _get_session():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT * FROM session_metadata WHERE id = ?", (session_id,))
                row = cursor.fetchone()
                if row:
                    return SessionDict(
                        id=row[0],
                        user_id=row[1],
                        title=row[2],
                        created_at=row[3],
                        updated_at=row[4],
                        last_activity=row[5],
                        message_count=row[6],
                        is_active=bool(row[7]),
                        metadata=self._deserialize_data(row[8])
                    )
                return None
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _get_session)
    
    async def update_session(self, session_id: str, title: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None,
                           increment_message_count: bool = False) -> bool:
        """更新会话"""
        def _update_session():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                updates = ["updated_at = ?", "last_activity = ?"]
                params = [datetime.now().isoformat(), datetime.now().isoformat()]
                
                if title is not None:
                    updates.append("title = ?")
                    params.append(title)
                
                if metadata is not None:
                    updates.append("metadata = ?")
                    params.append(self._serialize_data(metadata))
                
                if increment_message_count:
                    updates.append("message_count = message_count + 1")
                
                params.append(session_id)
                
                cursor.execute(f"""
                    UPDATE session_metadata 
                    SET {', '.join(updates)}
                    WHERE id = ?
                """, params)
                
                conn.commit()
                return cursor.rowcount > 0
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _update_session)
    
    async def list_user_sessions(self, user_id: str, limit: int = 50, 
                               offset: int = 0) -> List[SessionDict]:
        """列出用户的会话"""
        def _list_sessions():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("""
                    SELECT * FROM session_metadata 
                    WHERE user_id = ? AND is_active = 1
                    ORDER BY last_activity DESC
                    LIMIT ? OFFSET ?
                """, (user_id, limit, offset))
                
                sessions = []
                for row in cursor.fetchall():
                    sessions.append(SessionDict(
                        id=row[0],
                        user_id=row[1],
                        title=row[2],
                        created_at=row[3],
                        updated_at=row[4],
                        last_activity=row[5],
                        message_count=row[6],
                        is_active=bool(row[7]),
                        metadata=self._deserialize_data(row[8])
                    ))
                return sessions
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _list_sessions)
    
    async def delete_session(self, session_id: str) -> bool:
        """删除会话及其相关数据"""
        def _delete_session():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                # 删除相关消息
                cursor.execute("DELETE FROM messages WHERE session_id = ?", (session_id,))
                
                # 删除会话
                cursor.execute("DELETE FROM session_metadata WHERE id = ?", (session_id,))
                
                conn.commit()
                return cursor.rowcount > 0
            finally:
                conn.close()
        
        return await asyncio.get_event_loop().run_in_executor(None, _delete_session)

    # 消息管理方法
    async def add_message(self, session_id: str, role: str, content: str,
                         metadata: Optional[Dict[str, Any]] = None) -> MessageDict:
        """添加消息到会话"""
        def _add_message():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                message_id = str(uuid.uuid4())
                created_at = datetime.now().isoformat()

                cursor.execute("""
                    INSERT INTO messages (id, session_id, role, content, created_at, metadata)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, (
                    message_id,
                    session_id,
                    role,
                    content,
                    created_at,
                    self._serialize_data(metadata or {})
                ))

                # 更新会话的消息计数和最后活动时间
                cursor.execute("""
                    UPDATE session_metadata
                    SET message_count = message_count + 1,
                        last_activity = ?,
                        updated_at = ?
                    WHERE id = ?
                """, (created_at, created_at, session_id))

                conn.commit()

                return MessageDict(
                    id=message_id,
                    session_id=session_id,
                    role=role,
                    content=content,
                    created_at=created_at,
                    metadata=metadata or {}
                )
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _add_message)

    async def get_session_messages(self, session_id: str, limit: int = 100,
                                 offset: int = 0) -> List[MessageDict]:
        """获取会话的消息历史"""
        def _get_messages():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("""
                    SELECT * FROM messages
                    WHERE session_id = ?
                    ORDER BY created_at ASC
                    LIMIT ? OFFSET ?
                """, (session_id, limit, offset))

                messages = []
                for row in cursor.fetchall():
                    messages.append(MessageDict(
                        id=row[0],
                        session_id=row[1],
                        role=row[2],
                        content=row[3],
                        created_at=row[4],
                        metadata=self._deserialize_data(row[5])
                    ))
                return messages
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _get_messages)

    async def update_session_title_if_needed(self, session_id: str, first_message: str) -> bool:
        """
        如果会话还没有标题，基于首条消息自动生成智能标题
        借鉴 LangGraphChainlitAgent 的智能命名逻辑
        """
        session = await self.get_session(session_id)
        if not session:
            return False

        # 如果已经有标题，跳过
        if session.get('title'):
            logger.info(f"🏷️ 会话已有标题，跳过更新: {session['title']}")
            return False

        # 生成智能标题
        new_title = await self.generate_intelligent_title(first_message)

        # 更新会话标题
        success = await self.update_session(session_id, title=new_title)
        if success:
            logger.info(f"✅ 会话标题已更新: {session_id} -> '{new_title}'")

        return success

    # 统计和监控方法
    async def get_user_session_count(self, user_id: str) -> int:
        """获取用户的会话数量"""
        def _get_count():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("""
                    SELECT COUNT(*) FROM session_metadata
                    WHERE user_id = ? AND is_active = 1
                """, (user_id,))
                return cursor.fetchone()[0]
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _get_count)

    async def get_session_message_count(self, session_id: str) -> int:
        """获取会话的消息数量"""
        def _get_count():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cursor.execute("SELECT COUNT(*) FROM messages WHERE session_id = ?", (session_id,))
                return cursor.fetchone()[0]
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _get_count)

    async def cleanup_inactive_sessions(self, days: int = 30) -> int:
        """清理不活跃的会话"""
        def _cleanup():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                cutoff_date = datetime.now().replace(day=datetime.now().day - days).isoformat()

                # 标记为不活跃而不是删除
                cursor.execute("""
                    UPDATE session_metadata
                    SET is_active = 0
                    WHERE last_activity < ? AND is_active = 1
                """, (cutoff_date,))

                conn.commit()
                return cursor.rowcount
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _cleanup)

    # 数据库维护方法
    async def vacuum_database(self):
        """优化数据库"""
        def _vacuum():
            conn = sqlite3.connect(self.db_path)
            try:
                conn.execute("VACUUM")
                conn.commit()
            finally:
                conn.close()

        await asyncio.get_event_loop().run_in_executor(None, _vacuum)

    async def get_database_stats(self) -> Dict[str, int]:
        """获取数据库统计信息"""
        def _get_stats():
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            try:
                stats = {}

                cursor.execute("SELECT COUNT(*) FROM users")
                stats['total_users'] = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM session_metadata WHERE is_active = 1")
                stats['active_sessions'] = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM session_metadata WHERE is_active = 0")
                stats['inactive_sessions'] = cursor.fetchone()[0]

                cursor.execute("SELECT COUNT(*) FROM messages")
                stats['total_messages'] = cursor.fetchone()[0]

                return stats
            finally:
                conn.close()

        return await asyncio.get_event_loop().run_in_executor(None, _get_stats)
