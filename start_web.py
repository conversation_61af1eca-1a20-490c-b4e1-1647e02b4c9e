#!/usr/bin/env python3
"""
Web API 启动脚本
专门用于启动Web API服务的入口文件
"""

import asyncio
import sys
import os
import signal
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 导入增强的Web API应用
from api.enhanced_web_api import create_enhanced_app as create_app
import uvicorn


def signal_handler(signum, frame):
    """信号处理器"""
    print(f"\n收到信号 {signum}，正在关闭Web API服务器...")
    sys.exit(0)


def show_startup_info():
    """显示启动信息"""
    print("\n🚀 启动增强Web API服务器...")
    print("=" * 60)
    print("📋 服务信息:")
    print("  - 名称: LangGraph Agent Enhanced Web API")
    print("  - 版本: 2.1.0")
    print("  - CLI模式: uv run main.py")
    print("  - Web API: http://localhost:8000")
    print("  - API文档: http://localhost:8000/docs")
    print("  - 健康检查: http://localhost:8000/health")
    print("  - WebSocket: ws://localhost:8000/ws/{session_id}")
    print("")
    print("🎯 增强功能特性:")
    print("  ✅ 完整会话管理 - 创建、读取、更新、删除会话")
    print("  ✅ 智能会话命名 - 自动生成有意义的会话标题")
    print("  ✅ 用户会话隔离 - 多用户独立会话管理")
    print("  ✅ 消息历史管理 - 完整的对话历史记录")
    print("  ✅ 实时WebSocket通信 - 双向实时消息推送")
    print("")
    print("🎯 可用的API端点:")
    print("  GET  /health - 健康检查")
    print("  POST /api/chat - 增强聊天接口（集成会话管理）")
    print("  POST /api/chat/stream - 流式聊天")
    print("  GET  /api/sessions/ - 获取会话列表")
    print("  POST /api/sessions/ - 创建新会话")
    print("  GET  /api/sessions/{session_id} - 获取会话详情")
    print("  PUT  /api/sessions/{session_id} - 更新会话")
    print("  DELETE /api/sessions/{session_id} - 删除会话")
    print("  GET  /api/sessions/{session_id}/messages - 获取会话消息")
    print("  GET  /api/sessions/stats/overview - 会话统计")
    print("  GET  /api/tools - 可用工具列表")
    print("  WS   /ws/{session_id} - WebSocket实时通信")
    print("")
    print("✨ 现在可以通过以下方式体验:")
    print("  1. 浏览器访问: http://localhost:8000/docs")
    print("  2. CLI命令: uv run main.py")
    print("  3. API调用示例:")
    print("     curl -X POST http://localhost:8000/api/chat \\")
    print("          -H 'Content-Type: application/json' \\")
    print("          -d '{\"message\": \"你好，请介绍一下这个系统\", \"user_id\": \"test_user\"}'")
    print("")
    print("🔥 正在启动增强服务器...")


async def start_server():
    """启动Web API服务器"""
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 显示启动信息
    show_startup_info()
    
    try:
        # 创建FastAPI应用
        app = create_app()
        
        # 配置uvicorn服务器
        config = uvicorn.Config(
            app, 
            host='0.0.0.0', 
            port=8000, 
            log_level='info',
            reload=False  # 生产模式不使用reload
        )
        
        # 启动服务器
        server = uvicorn.Server(config)
        await server.serve()
        
    except Exception as e:
        print(f"❌ 启动Web API服务器失败: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def main():
    """主函数"""
    try:
        # 运行异步服务器
        asyncio.run(start_server())
    except KeyboardInterrupt:
        print("\n👋 Web API服务器已关闭")
    except Exception as e:
        print(f"❌ 服务器运行错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
