"""
会话管理API测试 - 第二阶段功能验证
测试新实现的会话CRUD API端点
"""

import asyncio
import pytest
import httpx
import json
from datetime import datetime
from typing import Dict, Any

# 测试配置
API_BASE_URL = "http://localhost:8000"
TEST_USER_ID = "test_user_api"


class SessionManagementAPITester:
    """会话管理API测试器"""
    
    def __init__(self, base_url: str = API_BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(base_url=base_url, timeout=30.0)
        self.created_sessions = []  # 跟踪创建的会话，用于清理
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.cleanup()
        await self.client.aclose()
    
    async def cleanup(self):
        """清理测试数据"""
        print("\n🧹 清理测试数据...")
        for session_id in self.created_sessions:
            try:
                await self.delete_session(session_id)
                print(f"  ✅ 清理会话: {session_id}")
            except Exception as e:
                print(f"  ⚠️ 清理会话失败 {session_id}: {e}")
    
    async def test_health_check(self):
        """测试健康检查端点"""
        print("\n🔍 测试健康检查...")
        
        response = await self.client.get("/health")
        assert response.status_code == 200
        
        data = response.json()
        assert data["status"] == "healthy"
        assert "version" in data
        
        print(f"  ✅ 健康检查通过: {data}")
        return True
    
    async def test_create_session(self, title: str = None) -> Dict[str, Any]:
        """测试创建会话"""
        print(f"\n📝 测试创建会话 (title: {title})...")
        
        request_data = {
            "user_id": TEST_USER_ID,
            "metadata": {"test": True, "created_at": datetime.now().isoformat()}
        }
        
        if title:
            request_data["title"] = title
        
        response = await self.client.post("/api/sessions/", json=request_data)
        assert response.status_code == 200
        
        session = response.json()
        assert "id" in session
        assert session["user_id"] == TEST_USER_ID
        assert session["is_active"] is True
        
        # 记录创建的会话
        self.created_sessions.append(session["id"])
        
        print(f"  ✅ 会话创建成功: {session['id']}")
        print(f"     标题: {session['title']}")
        print(f"     创建时间: {session['created_at']}")
        
        return session
    
    async def test_get_session(self, session_id: str) -> Dict[str, Any]:
        """测试获取会话详情"""
        print(f"\n🔍 测试获取会话详情: {session_id}...")
        
        response = await self.client.get(f"/api/sessions/{session_id}")
        assert response.status_code == 200
        
        session = response.json()
        assert session["id"] == session_id
        assert session["user_id"] == TEST_USER_ID
        
        print(f"  ✅ 获取会话详情成功:")
        print(f"     ID: {session['id']}")
        print(f"     标题: {session['title']}")
        print(f"     消息数: {session['message_count']}")
        
        return session
    
    async def test_list_sessions(self) -> Dict[str, Any]:
        """测试获取会话列表"""
        print(f"\n📋 测试获取会话列表 (用户: {TEST_USER_ID})...")
        
        response = await self.client.get(f"/api/sessions/?user_id={TEST_USER_ID}")
        assert response.status_code == 200
        
        data = response.json()
        assert "sessions" in data
        assert "total" in data
        assert isinstance(data["sessions"], list)
        
        print(f"  ✅ 获取会话列表成功:")
        print(f"     总数: {data['total']}")
        print(f"     当前页会话数: {len(data['sessions'])}")
        
        for session in data["sessions"][:3]:  # 只显示前3个
            print(f"     - {session['id']}: {session['title']}")
        
        return data
    
    async def test_update_session(self, session_id: str, new_title: str) -> Dict[str, Any]:
        """测试更新会话"""
        print(f"\n✏️ 测试更新会话: {session_id}...")
        
        request_data = {
            "title": new_title,
            "metadata": {"updated": True, "updated_at": datetime.now().isoformat()}
        }
        
        response = await self.client.put(f"/api/sessions/{session_id}", json=request_data)
        assert response.status_code == 200
        
        session = response.json()
        assert session["id"] == session_id
        assert session["title"] == new_title
        
        print(f"  ✅ 会话更新成功:")
        print(f"     新标题: {session['title']}")
        
        return session
    
    async def test_add_message(self, session_id: str, role: str, content: str) -> Dict[str, Any]:
        """测试添加消息"""
        print(f"\n💬 测试添加消息到会话: {session_id}...")
        
        request_data = {
            "role": role,
            "content": content,
            "metadata": {"test_message": True}
        }
        
        response = await self.client.post(
            f"/api/sessions/{session_id}/messages",
            params=request_data
        )
        assert response.status_code == 200
        
        message = response.json()
        assert message["session_id"] == session_id
        assert message["role"] == role
        assert message["content"] == content
        
        print(f"  ✅ 消息添加成功:")
        print(f"     角色: {message['role']}")
        print(f"     内容: {message['content'][:50]}...")
        
        return message
    
    async def test_get_session_messages(self, session_id: str) -> Dict[str, Any]:
        """测试获取会话消息"""
        print(f"\n📜 测试获取会话消息: {session_id}...")
        
        response = await self.client.get(f"/api/sessions/{session_id}/messages")
        assert response.status_code == 200
        
        data = response.json()
        assert data["session_id"] == session_id
        assert "messages" in data
        assert isinstance(data["messages"], list)
        
        print(f"  ✅ 获取会话消息成功:")
        print(f"     消息总数: {data['total_messages']}")
        
        for i, message in enumerate(data["messages"][:3]):  # 只显示前3条
            print(f"     {i+1}. [{message['role']}] {message['content'][:30]}...")
        
        return data
    
    async def test_session_stats(self) -> Dict[str, Any]:
        """测试获取会话统计"""
        print("\n📊 测试获取会话统计...")
        
        response = await self.client.get("/api/sessions/stats/overview")
        assert response.status_code == 200
        
        stats = response.json()
        assert "total_sessions" in stats
        assert "active_sessions" in stats
        assert "total_messages" in stats
        assert "total_users" in stats
        
        print(f"  ✅ 获取会话统计成功:")
        print(f"     总会话数: {stats['total_sessions']}")
        print(f"     活跃会话数: {stats['active_sessions']}")
        print(f"     总消息数: {stats['total_messages']}")
        print(f"     总用户数: {stats['total_users']}")
        
        return stats
    
    async def delete_session(self, session_id: str) -> Dict[str, Any]:
        """删除会话"""
        response = await self.client.delete(f"/api/sessions/{session_id}")
        assert response.status_code == 200
        
        result = response.json()
        assert result["session_id"] == session_id
        assert result["deleted"] is True
        
        return result
    
    async def test_enhanced_chat_api(self) -> Dict[str, Any]:
        """测试增强的聊天API"""
        print("\n🤖 测试增强的聊天API...")
        
        request_data = {
            "message": "你好，这是一个测试消息，请简单回复",
            "user_id": TEST_USER_ID,
            "auto_title": True
        }
        
        response = await self.client.post("/api/chat", json=request_data)
        assert response.status_code == 200
        
        chat_response = response.json()
        assert "message" in chat_response
        assert "session_id" in chat_response
        assert chat_response["user_id"] == TEST_USER_ID
        assert chat_response["is_new_session"] is True
        
        # 记录创建的会话
        self.created_sessions.append(chat_response["session_id"])
        
        print(f"  ✅ 增强聊天API测试成功:")
        print(f"     会话ID: {chat_response['session_id']}")
        print(f"     会话标题: {chat_response.get('session_title', 'N/A')}")
        print(f"     AI回复: {chat_response['message'][:50]}...")
        
        return chat_response


async def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 开始会话管理API综合测试")
    print("=" * 60)
    
    async with SessionManagementAPITester() as tester:
        try:
            # 1. 健康检查
            await tester.test_health_check()
            
            # 2. 创建会话
            session1 = await tester.test_create_session("测试会话1")
            session2 = await tester.test_create_session()  # 无标题
            
            # 3. 获取会话详情
            await tester.test_get_session(session1["id"])
            
            # 4. 添加消息
            await tester.test_add_message(session1["id"], "user", "这是第一条用户消息")
            await tester.test_add_message(session1["id"], "assistant", "这是AI的回复消息")
            
            # 5. 获取会话消息
            await tester.test_get_session_messages(session1["id"])
            
            # 6. 更新会话
            await tester.test_update_session(session1["id"], "更新后的会话标题")
            
            # 7. 获取会话列表
            await tester.test_list_sessions()
            
            # 8. 获取统计信息
            await tester.test_session_stats()
            
            # 9. 测试增强的聊天API
            await tester.test_enhanced_chat_api()
            
            print("\n" + "=" * 60)
            print("🎉 所有测试通过！会话管理API功能正常")
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    return True


if __name__ == "__main__":
    # 运行测试
    success = asyncio.run(run_comprehensive_test())
    exit(0 if success else 1)
