"""
CLI集成测试
测试CLI功能的完整集成
"""
import pytest
import asyncio
import tempfile
import subprocess
import time
import os
import sys
from pathlib import Path
from unittest.mock import patch, Mock

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

@pytest.mark.integration
@pytest.mark.cli
class TestCLIIntegration:
    """CLI集成测试类"""
    
    @pytest.mark.asyncio
    async def test_cli_startup(self, test_config_dir):
        """测试CLI启动"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main

            # 测试初始化函数
            agent, tools, session_manager = await main.initialize_agent()

            assert agent is not None
            assert session_manager is not None
            assert hasattr(agent, 'ainvoke')
            assert hasattr(agent, 'astream')

        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_session_management_integration(self, test_config_dir):
        """测试会话管理集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main

            agent, tools, session_manager = await main.initialize_agent()

            # 创建新会话
            session_id = session_manager.create_session("test_user")
            assert session_id is not None

            # 获取会话配置
            config = session_manager.get_session_config(session_id)
            assert config is not None
            assert 'configurable' in config

            # 验证会话管理器功能
            assert hasattr(session_manager, 'create_session')
            assert hasattr(session_manager, 'get_session_config')

        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_agent_workflow_integration(self, test_config_dir):
        """测试智能体工作流集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)
        
        try:
            import main
            from langchain_core.messages import HumanMessage
            
            agent, tools, session_manager = await main.initialize_agent()
            
            # 创建会话
            session_id = session_manager.create_session("test_user")
            config = session_manager.get_session_config(session_id)
            
            # 测试状态获取
            state = await agent.aget_state(config)
            assert state is not None
            
            # 测试状态更新
            test_message = HumanMessage(content="测试消息")
            await agent.aupdate_state(config, {"messages": [test_message]})
            
            # 验证状态已更新
            updated_state = await agent.aget_state(config)
            assert "messages" in updated_state.values
            assert len(updated_state.values["messages"]) > 0
            
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_persistence_integration(self, test_config_dir):
        """测试持久化集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)
        
        try:
            import main
            
            # 加载持久化配置
            persistence_config = main.load_persistence_config()
            assert persistence_config is not None
            
            # 创建检查点存储器
            checkpointer = await main.create_checkpointer()
            assert checkpointer is not None
            
            # 验证检查点存储器方法
            assert hasattr(checkpointer, 'aput')
            assert hasattr(checkpointer, 'aget')
            assert hasattr(checkpointer, 'alist')
            
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_tool_loading_integration(self, test_config_dir):
        """测试工具加载集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            from utils.mcp_loader import load_mcp_tools_from_config

            # 加载MCP工具
            _, tools = await load_mcp_tools_from_config("mcp_config.json")

            assert tools is not None
            assert len(tools) > 0

            # 验证工具属性
            for tool in tools[:3]:  # 检查前3个工具
                assert hasattr(tool, 'name')
                assert hasattr(tool, 'description')
                assert callable(tool)

        finally:
            os.chdir(original_cwd)

@pytest.mark.integration
@pytest.mark.api
class TestWebAPIIntegration:
    """Web API集成测试类"""
    
    @pytest.mark.asyncio
    async def test_fastapi_startup(self):
        """测试FastAPI启动"""
        try:
            from api.enhanced_web_api import create_enhanced_app

            app = create_enhanced_app()
            assert app is not None

            # 验证路由
            routes = [route.path for route in app.routes]
            assert "/health" in routes
            assert "/api/chat" in routes
            assert "/api/sessions/" in routes  # 更新为新的会话管理端点

        except ImportError:
            pytest.skip("增强Web API组件未安装")
    
    @pytest.mark.asyncio
    async def test_websocket_integration(self):
        """测试WebSocket集成"""
        try:
            from interfaces.web_api import create_app
            from fastapi.testclient import TestClient
            
            app = create_app()
            client = TestClient(app)
            
            # 测试WebSocket连接
            with client.websocket_connect("/ws/test_thread_123") as websocket:
                # 发送测试消息
                websocket.send_json({
                    "type": "message",
                    "content": "测试消息",
                    "session_id": "test_session"
                })
                
                # 接收响应
                response = websocket.receive_json()
                assert response is not None
                
        except ImportError:
            pytest.skip("Web API组件未安装")

@pytest.mark.integration
@pytest.mark.langgraph
class TestLangGraphIntegration:
    """LangGraph集成测试类"""
    
    @pytest.mark.asyncio
    async def test_complete_workflow_integration(self, test_config_dir):
        """测试完整工作流集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main
            from langchain_core.messages import HumanMessage

            # 初始化完整系统
            agent, tools, session_manager = await main.initialize_agent()
            
            # 创建会话
            session_id = session_manager.create_session("integration_test_user")
            config = session_manager.get_session_config(session_id)
            
            # 测试完整的消息处理流程
            test_message = HumanMessage(content="这是一个集成测试消息")
            
            # 更新状态
            await agent.aupdate_state(config, {"messages": [test_message]})
            
            # 获取状态
            state = await agent.aget_state(config)
            assert state is not None
            assert "messages" in state.values
            
            # 验证消息已保存
            messages = state.values["messages"]
            assert len(messages) > 0
            assert any(msg.content == "这是一个集成测试消息" for msg in messages)
            
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_persistence_workflow_integration(self, test_config_dir):
        """测试持久化工作流集成"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main
            from langchain_core.messages import HumanMessage

            # 第一次初始化
            agent1, tools1, session_manager1 = await main.initialize_agent()
            session_id = session_manager1.create_session("persistence_test_user")
            config = session_manager1.get_session_config(session_id)

            # 添加消息
            test_message = HumanMessage(content="持久化测试消息")
            await agent1.aupdate_state(config, {"messages": [test_message]})

            # 第二次初始化（模拟重启）
            agent2, tools2, session_manager2 = await main.initialize_agent()
            
            # 验证会话和消息是否持久化
            state = await agent2.aget_state(config)
            assert state is not None
            assert "messages" in state.values
            
            messages = state.values["messages"]
            assert len(messages) > 0
            assert any(msg.content == "持久化测试消息" for msg in messages)
            
        finally:
            os.chdir(original_cwd)

@pytest.mark.integration
@pytest.mark.slow
class TestSystemIntegration:
    """系统级集成测试"""
    
    @pytest.mark.asyncio
    async def test_full_system_startup(self, test_config_dir):
        """测试完整系统启动"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main

            # 测试完整启动流程
            start_time = time.time()

            agent, tools, session_manager = await main.initialize_agent()

            startup_time = time.time() - start_time

            # 验证启动时间合理（应该在30秒内）
            assert startup_time < 30.0

            # 验证所有组件正常
            assert agent is not None
            assert session_manager is not None

            # 验证工具加载
            assert len(tools) > 0
            
        finally:
            os.chdir(original_cwd)
    
    @pytest.mark.asyncio
    async def test_concurrent_sessions(self, test_config_dir):
        """测试并发会话"""
        original_cwd = os.getcwd()
        os.chdir(test_config_dir)

        try:
            import main
            from langchain_core.messages import HumanMessage

            agent, tools, session_manager = await main.initialize_agent()
            
            # 创建多个并发会话
            session_ids = []
            for i in range(5):
                session_id = session_manager.create_session(f"concurrent_user_{i}")
                session_ids.append(session_id)
            
            # 并发处理消息
            async def process_session(session_id, message_content):
                config = session_manager.get_session_config(session_id)
                message = HumanMessage(content=message_content)
                await agent.aupdate_state(config, {"messages": [message]})
                return await agent.aget_state(config)
            
            # 并发执行
            tasks = []
            for i, session_id in enumerate(session_ids):
                task = process_session(session_id, f"并发消息 {i}")
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            # 验证所有会话都正常处理
            assert len(results) == 5
            for result in results:
                assert result is not None
                assert "messages" in result.values
            
        finally:
            os.chdir(original_cwd)

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
