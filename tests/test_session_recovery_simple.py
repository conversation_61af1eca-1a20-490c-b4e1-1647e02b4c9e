"""
简化的会话恢复功能测试
直接测试服务层功能
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_data_layer import EnhancedDataLayer
from services.session_recovery_service import SessionRecoveryService
from core.agent_core import AgentCore


async def test_session_recovery_service():
    """测试会话恢复服务"""
    print("🧪 开始测试会话恢复服务...")
    
    # 初始化服务
    data_layer = EnhancedDataLayer()
    agent_core = AgentCore()
    recovery_service = SessionRecoveryService(data_layer, agent_core)
    
    test_user_id = "test_recovery_user"
    test_session_ids = []
    
    try:
        # 1. 创建测试用户
        print("📝 创建测试用户...")
        user = await data_layer.get_user_by_identifier(test_user_id)
        if not user:
            user = await data_layer.create_user(
                identifier=test_user_id,
                display_name="测试用户",
                metadata={"test": True}
            )
        print(f"✅ 用户创建成功: {user['id']}")
        
        # 2. 创建测试会话
        print("📝 创建测试会话...")
        session_result = await data_layer.create_session(
            user_id=user["id"],
            title="恢复测试会话"
        )
        
        # 处理返回值
        if isinstance(session_result, str):
            session_id = session_result
        else:
            session_id = session_result["id"]
            
        test_session_ids.append(session_id)
        print(f"✅ 会话创建成功: {session_id}")
        
        # 3. 添加测试消息
        print("📝 添加测试消息...")
        await data_layer.add_message(
            session_id=session_id,
            role="user",
            content="你好，这是第一条测试消息",
            metadata={"test": True}
        )
        
        await data_layer.add_message(
            session_id=session_id,
            role="assistant",
            content="你好！我是AI助手，很高兴为您服务。",
            metadata={"test": True}
        )
        print("✅ 测试消息添加成功")
        
        # 4. 测试获取可恢复会话列表
        print("📝 测试获取可恢复会话列表...")
        recoverable_sessions = await recovery_service.get_recoverable_sessions(test_user_id)
        print(f"✅ 可恢复会话数量: {len(recoverable_sessions)}")
        
        for session in recoverable_sessions:
            print(f"   - 会话: {session['session_id'][:8]}... 标题: {session['title']} 消息数: {session['message_count']}")
        
        # 5. 测试会话完整性检查
        print("📝 测试会话完整性检查...")
        integrity_result = await recovery_service.validate_session_integrity(session_id)
        print(f"✅ 会话完整性: {integrity_result['valid']}")
        if integrity_result['valid']:
            print(f"   - 消息数量: {integrity_result['message_count']}")
            print(f"   - 元数据存在: {integrity_result['metadata_exists']}")
        
        # 6. 测试会话上下文恢复
        print("📝 测试会话上下文恢复...")
        recovery_result = await recovery_service.restore_session_context(session_id)
        print(f"✅ 恢复结果: {recovery_result['success']}")
        
        if recovery_result['success']:
            print(f"   - 恢复消息数: {recovery_result['message_count']}")
            print(f"   - 恢复时间: {recovery_result['recovery_time']}")
            print(f"   - 会话标题: {recovery_result['session_info']['title']}")
        else:
            print(f"   - 错误信息: {recovery_result.get('error', '未知错误')}")
        
        # 7. 测试不存在会话的恢复
        print("📝 测试不存在会话的恢复...")
        nonexistent_result = await recovery_service.restore_session_context("nonexistent_session")
        print(f"✅ 不存在会话处理: {not nonexistent_result['success']}")
        if not nonexistent_result['success']:
            print(f"   - 错误信息: {nonexistent_result['error']}")
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理测试数据
        print("🧹 清理测试数据...")
        try:
            from services.session_deletion_service_simple import SessionDeletionService
            deletion_service = SessionDeletionService(data_layer)
            
            for session_id in test_session_ids:
                try:
                    await deletion_service.hard_delete_session(session_id)
                    print(f"✅ 清理会话: {session_id}")
                except Exception as e:
                    print(f"⚠️ 清理会话失败: {e}")
                    
        except Exception as e:
            print(f"⚠️ 清理过程出错: {e}")


async def test_basic_data_layer():
    """测试基础数据层功能"""
    print("🧪 测试基础数据层功能...")
    
    data_layer = EnhancedDataLayer()
    
    try:
        # 测试用户创建
        user = await data_layer.create_user(
            identifier="test_basic_user",
            display_name="基础测试用户"
        )
        print(f"✅ 用户创建: {user['id']}")
        
        # 测试会话创建
        session_result = await data_layer.create_session(
            user_id=user["id"],
            title="基础测试会话"
        )
        
        if isinstance(session_result, str):
            session_id = session_result
        else:
            session_id = session_result["id"]
            
        print(f"✅ 会话创建: {session_id}")
        
        # 测试消息添加
        await data_layer.add_message(
            session_id=session_id,
            role="user",
            content="测试消息"
        )
        print("✅ 消息添加成功")
        
        # 测试会话获取
        session = await data_layer.get_session(session_id)
        print(f"✅ 会话获取: {session['title']}")
        
        # 测试消息获取
        messages = await data_layer.get_session_messages(session_id)
        print(f"✅ 消息获取: {len(messages)} 条消息")
        
    except Exception as e:
        print(f"❌ 基础测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("🚀 开始会话恢复功能测试")
    
    # 先测试基础功能
    print("\n" + "="*50)
    asyncio.run(test_basic_data_layer())
    
    # 再测试恢复功能
    print("\n" + "="*50)
    asyncio.run(test_session_recovery_service())
    
    print("\n✨ 测试完成！")
