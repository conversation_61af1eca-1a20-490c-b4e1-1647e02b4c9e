"""
第二阶段完整功能测试
验证所有第二阶段功能的集成测试
"""

import asyncio
import os
import sys
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_data_layer import EnhancedDataLayer
from services.session_recovery_service import SessionRecoveryService
from services.session_monitoring_service import SessionMonitoringService
from core.agent_core import AgentCore


async def test_phase2_complete():
    """第二阶段完整功能测试"""
    print("🚀 开始第二阶段完整功能测试\n")
    print("="*60)
    print("测试范围:")
    print("✅ Task 2.1: Session CRUD API Endpoints")
    print("✅ Task 2.2: Session Deletion Functionality") 
    print("✅ Task 2.3: Historical Session Recovery Mechanism")
    print("✅ Task 2.4: Session State Monitoring")
    print("="*60)
    
    # 初始化服务
    data_layer = EnhancedDataLayer("./data/test_phase2_complete.db")
    agent_core = AgentCore()  # 创建AgentCore实例
    recovery_service = SessionRecoveryService(data_layer, agent_core)
    monitoring_service = SessionMonitoringService(data_layer)
    
    try:
        print("\n📝 第一步: 创建测试数据...")
        
        # 创建测试用户
        user1 = await data_layer.create_user(
            identifier="phase2_user_1",
            display_name="第二阶段测试用户1"
        )
        
        user2 = await data_layer.create_user(
            identifier="phase2_user_2", 
            display_name="第二阶段测试用户2"
        )
        
        print(f"✅ 创建用户: {user1['display_name']}, {user2['display_name']}")
        
        # 创建测试会话
        session1 = await data_layer.create_session(
            user_id=user1['id'],
            title="完整测试会话1"
        )
        
        session2 = await data_layer.create_session(
            user_id=user2['id'],
            title="完整测试会话2"
        )
        
        session3 = await data_layer.create_session(
            user_id=user1['id'],
            title="待删除测试会话"
        )
        
        print(f"✅ 创建会话: 3个测试会话")
        
        # 添加测试消息
        messages = [
            (session1['id'], "user", "你好，这是第一个会话的消息"),
            (session1['id'], "assistant", "你好！我是AI助手，很高兴为您服务"),
            (session1['id'], "user", "请帮我分析一下数据"),
            (session1['id'], "assistant", "当然可以，请提供您需要分析的数据"),
            
            (session2['id'], "user", "第二个会话的测试消息"),
            (session2['id'], "assistant", "收到您的消息，有什么可以帮助您的吗？"),
            
            (session3['id'], "user", "这个会话将被删除"),
            (session3['id'], "assistant", "这是一个测试回复"),
        ]
        
        for session_id, role, content in messages:
            await data_layer.add_message(session_id, role, content)
        
        print(f"✅ 添加消息: {len(messages)} 条测试消息")
        
        print("\n" + "="*50)
        print("🧪 第二步: 测试Task 2.1 - Session CRUD API...")
        
        # 测试会话列表
        sessions = await data_layer.list_user_sessions(user1['id'])
        print(f"✅ 用户1的会话列表: {len(sessions)} 个会话")
        
        # 测试会话获取
        session_detail = await data_layer.get_session(session1['id'])
        print(f"✅ 会话详情获取: {session_detail['title']}")
        
        # 测试会话更新
        await data_layer.update_session(session1['id'], title="更新后的会话标题")
        updated_session = await data_layer.get_session(session1['id'])
        print(f"✅ 会话标题更新: {updated_session['title']}")
        
        # 测试消息获取
        session_messages = await data_layer.get_session_messages(session1['id'])
        print(f"✅ 会话消息获取: {len(session_messages)} 条消息")
        
        print("\n" + "="*50)
        print("🧪 第三步: 测试Task 2.2 - Session Deletion...")
        
        # 测试会话删除
        deletion_result = await data_layer.delete_session(session3['id'])
        print(f"✅ 会话删除: {deletion_result}")

        # 验证删除后的状态
        deleted_session = await data_layer.get_session(session3['id'])
        if deleted_session is None:
            print("✅ 会话删除状态验证: 会话已删除")
        else:
            print("❌ 会话删除状态验证失败")
        
        print("\n" + "="*50)
        print("🧪 第四步: 测试Task 2.3 - Session Recovery...")
        
        # 测试会话恢复
        recovery_result = await recovery_service.restore_session_context(session1['id'])
        print(f"✅ 会话恢复测试: {recovery_result.get('success', False)}")

        # 测试会话完整性验证
        integrity_result = await recovery_service.validate_session_integrity(session1['id'])
        print(f"✅ 会话完整性验证: {integrity_result.get('valid', False)}")
        
        # 测试可恢复会话列表
        recoverable_sessions = await recovery_service.get_recoverable_sessions(user1['id'])
        print(f"✅ 可恢复会话列表: {len(recoverable_sessions)} 个会话")
        
        print("\n" + "="*50)
        print("🧪 第五步: 测试Task 2.4 - Session Monitoring...")
        
        # 测试活跃会话统计
        active_stats = await monitoring_service.get_active_sessions_count(24)
        print(f"✅ 活跃会话统计:")
        print(f"   - 活跃会话数: {active_stats.get('active_sessions', 0)}")
        print(f"   - 活跃用户数: {active_stats.get('active_users', 0)}")
        print(f"   - 活跃率: {active_stats.get('activity_rate', 0)}%")
        
        # 测试会话持续时间分析
        duration_analysis = await monitoring_service.get_session_duration_analysis(10)
        print(f"✅ 会话持续时间分析:")
        print(f"   - 分析会话数: {duration_analysis.get('total_analyzed_sessions', 0)}")
        print(f"   - 有消息的会话数: {duration_analysis.get('active_sessions_with_messages', 0)}")
        
        # 测试用户活跃度分析
        user_analysis = await monitoring_service.get_user_activity_analysis(10)
        print(f"✅ 用户活跃度分析:")
        print(f"   - 分析用户数: {user_analysis.get('total_analyzed_users', 0)}")
        print(f"   - 人均会话数: {user_analysis.get('average_sessions_per_user', 0)}")
        
        # 测试系统健康指标
        health_metrics = await monitoring_service.get_system_health_metrics()
        print(f"✅ 系统健康指标:")
        print(f"   - 系统状态: {health_metrics.get('system_status', 'unknown')}")
        print(f"   - 数据库大小: {health_metrics.get('database_size_mb', 0)} MB")
        
        print("\n" + "="*50)
        print("🎯 第六步: 综合功能验证...")
        
        # 验证数据一致性
        user1_sessions = await data_layer.list_user_sessions(user1['id'])
        user2_sessions = await data_layer.list_user_sessions(user2['id'])
        total_sessions = len(user1_sessions) + len(user2_sessions)
        print(f"✅ 数据一致性验证: 总会话数 {total_sessions}")
        
        # 验证监控数据准确性
        db_stats = await data_layer.get_database_stats()
        print(f"✅ 数据库统计验证:")
        print(f"   - 总用户数: {db_stats.get('total_users', 0)}")
        print(f"   - 活跃会话数: {db_stats.get('active_sessions', 0)}")
        print(f"   - 总消息数: {db_stats.get('total_messages', 0)}")
        
        print("\n🎉 第二阶段所有功能测试通过！")
        print("\n📋 测试总结:")
        print("✅ Task 2.1: Session CRUD API Endpoints - 完成")
        print("✅ Task 2.2: Session Deletion Functionality - 完成")
        print("✅ Task 2.3: Historical Session Recovery Mechanism - 完成")
        print("✅ Task 2.4: Session State Monitoring - 完成")
        print("\n🚀 第二阶段优化完成，可以进入第四阶段！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        try:
            if os.path.exists("./data/test_phase2_complete.db"):
                os.remove("./data/test_phase2_complete.db")
                print("✅ 测试数据库已删除")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")
        
        print("\n✨ 第二阶段完整测试完成！")


if __name__ == "__main__":
    asyncio.run(test_phase2_complete())
