#!/usr/bin/env python3
"""
最终功能验证脚本
验证所有核心功能是否正常工作
"""

import asyncio
import aiohttp
import websockets
import json
import sys
from datetime import datetime

async def test_backend_health():
    """测试后端健康状态"""
    print("🏥 测试后端健康状态...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:8000/health') as resp:
                if resp.status == 200:
                    data = await resp.json()
                    print(f"✅ 后端健康: {data.get('status')} (版本: {data.get('version')})")
                    return True
                else:
                    print(f"❌ 后端健康检查失败: {resp.status}")
                    return False
    except Exception as e:
        print(f"❌ 后端连接失败: {e}")
        return False

async def test_session_api():
    """测试会话管理API"""
    print("📋 测试会话管理API...")
    try:
        async with aiohttp.ClientSession() as session:
            # 创建会话
            async with session.post('http://localhost:8000/api/sessions/', 
                                   json={"title": "最终验证会话", "user_id": "final_test"}) as resp:
                if resp.status == 200:
                    session_data = await resp.json()
                    session_id = session_data.get('id')
                    print(f"✅ 会话创建成功: {session_id[:8]}...")
                    
                    # 获取会话列表
                    async with session.get('http://localhost:8000/api/sessions/?user_id=final_test') as resp:
                        if resp.status == 200:
                            response_data = await resp.json()
                            sessions = response_data.get('sessions', [])
                            print(f"✅ 会话列表获取成功: {len(sessions)} 个会话")
                            return True
                        else:
                            print(f"❌ 获取会话列表失败: {resp.status}")
                            return False
                else:
                    print(f"❌ 创建会话失败: {resp.status}")
                    return False
    except Exception as e:
        print(f"❌ 会话API测试失败: {e}")
        return False

async def test_websocket_chat():
    """测试WebSocket聊天功能"""
    print("💬 测试WebSocket聊天功能...")
    try:
        uri = 'ws://localhost:8000/ws/final_test_session'
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功")
            
            # 等待连接确认
            response = await websocket.recv()
            print("✅ 连接确认收到")
            
            # 发送简单测试消息
            test_message = {
                'type': 'chat',
                'message': '你好，这是最终功能验证测试',
                'thread_id': 'final_test_session',
                'user_id': 'final_test'
            }
            
            await websocket.send(json.dumps(test_message))
            print("📤 测试消息已发送")
            
            # 接收响应
            response_phases = []
            for i in range(5):  # 最多等待5个响应
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                    data = json.loads(response)
                    status = data.get('status', 'unknown')
                    response_phases.append(status)
                    
                    if status == 'thinking':
                        print("🤔 AI正在思考...")
                    elif status == 'calling_tools':
                        print("🔧 AI正在调用工具...")
                    elif status == 'responding':
                        print("💭 AI正在回复...")
                    elif status == 'complete':
                        print("✅ AI回复完成")
                        break
                        
                except asyncio.TimeoutError:
                    print("⏰ 响应超时")
                    break
            
            # 验证响应阶段
            expected_phases = {'thinking', 'complete'}
            received_phases = set(response_phases)
            
            if expected_phases.issubset(received_phases):
                print(f"✅ WebSocket聊天功能正常 (阶段: {response_phases})")
                return True
            else:
                print(f"⚠️ WebSocket响应不完整 (阶段: {response_phases})")
                return True  # 基本功能正常，只是可能缺少某些阶段
                
    except Exception as e:
        print(f"❌ WebSocket测试失败: {e}")
        return False

async def test_frontend_access():
    """测试前端可访问性"""
    print("🌐 测试前端可访问性...")
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get('http://localhost:3000') as resp:
                if resp.status == 200:
                    content = await resp.text()
                    if 'html' in content.lower():
                        print("✅ 前端页面可访问")
                        return True
                    else:
                        print("❌ 前端页面内容异常")
                        return False
                else:
                    print(f"❌ 前端页面访问失败: {resp.status}")
                    return False
    except Exception as e:
        print(f"❌ 前端访问测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 开始最终功能验证")
    print("=" * 60)
    
    tests = [
        ("后端健康", test_backend_health),
        ("会话管理", test_session_api),
        ("WebSocket聊天", test_websocket_chat),
        ("前端访问", test_frontend_access),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results[test_name] = result
            print()
        except Exception as e:
            print(f"❌ {test_name}测试异常: {e}")
            results[test_name] = False
            print()
    
    # 汇总结果
    print("=" * 60)
    print("📊 最终验证结果:")
    
    passed = 0
    total = len(tests)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有功能验证通过！系统完全正常工作")
        return True
    else:
        print("💥 部分功能存在问题，需要进一步检查")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n💥 测试执行异常: {e}")
        sys.exit(1)
