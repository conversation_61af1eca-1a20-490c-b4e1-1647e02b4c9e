#!/usr/bin/env python3
"""
第四阶段Task 4.1测试：前端API功能扩展测试
验证前端API服务是否正确集成了第二阶段的新功能
"""

import asyncio
import json
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from services.enhanced_data_layer import EnhancedDataLayer
from services.session_monitoring_service import SessionMonitoringService
from services.session_recovery_service import SessionRecoveryService
from core.agent_core import AgentCore

async def test_frontend_api_integration():
    """测试前端API集成功能"""
    print("🧪 开始测试第四阶段Task 4.1：前端API功能扩展")
    print("=" * 60)
    
    # 初始化服务
    data_layer = EnhancedDataLayer("./data/enhanced_sessions.db")
    agent_core = AgentCore()
    monitoring_service = SessionMonitoringService(data_layer)
    recovery_service = SessionRecoveryService(data_layer, agent_core)
    
    # 测试计数器
    tests_passed = 0
    total_tests = 0
    
    # === 测试1: 监控API功能 ===
    print("\n📊 测试1: 监控API功能")
    total_tests += 1
    
    try:
        # 测试活跃会话统计
        active_stats = await monitoring_service.get_active_sessions_count(24)
        print(f"✅ 活跃会话统计: {active_stats}")
        
        # 测试会话持续时间分析
        duration_analysis = await monitoring_service.get_session_duration_analysis(100)
        print(f"✅ 会话持续时间分析: 分析了 {duration_analysis.get('total_sessions', 0)} 个会话")
        
        # 测试消息频率统计
        frequency_stats = await monitoring_service.get_message_frequency_stats(7)
        print(f"✅ 消息频率统计: 总消息数 {frequency_stats.get('total_messages', 0)}")
        
        # 测试用户活跃度分析
        user_activity = await monitoring_service.get_user_activity_analysis(50)
        print(f"✅ 用户活跃度分析: 分析了 {user_activity.get('total_users', 0)} 个用户")
        
        # 测试系统健康指标
        health_metrics = await monitoring_service.get_system_health_metrics()
        print(f"✅ 系统健康指标: 状态 {health_metrics.get('status', 'unknown')}")
        
        tests_passed += 1
        print("✅ 监控API功能测试通过")
        
    except Exception as e:
        print(f"❌ 监控API功能测试失败: {e}")
    
    # === 测试2: 会话恢复API功能 ===
    print("\n🔄 测试2: 会话恢复API功能")
    total_tests += 1
    
    try:
        # 创建测试会话
        test_user_id = "test_user"
        session_result = await data_layer.create_session(
            user_id=test_user_id,
            title="测试恢复会话",
            metadata={"mode": "single_agent"}
        )
        test_session_id = session_result["id"]
        
        # 添加测试消息
        await data_layer.add_message(
            session_id=test_session_id,
            role="user",
            content="测试消息1"
        )
        await data_layer.add_message(
            session_id=test_session_id,
            role="assistant", 
            content="测试回复1"
        )
        
        # 测试会话完整性检查
        integrity_result = await recovery_service.validate_session_integrity(test_session_id)
        print(f"✅ 会话完整性检查: {integrity_result}")
        
        # 测试可恢复会话列表
        recoverable_sessions = await recovery_service.get_recoverable_sessions("test_user")
        print(f"✅ 可恢复会话列表: 找到 {len(recoverable_sessions)} 个会话")
        
        # 测试会话上下文恢复
        recovery_result = await recovery_service.restore_session_context(test_session_id)
        print(f"✅ 会话上下文恢复: {recovery_result}")
        
        tests_passed += 1
        print("✅ 会话恢复API功能测试通过")
        
    except Exception as e:
        print(f"❌ 会话恢复API功能测试失败: {e}")
    
    # === 测试3: 前端API接口格式验证 ===
    print("\n🔍 测试3: 前端API接口格式验证")
    total_tests += 1
    
    try:
        # 验证监控数据格式
        active_stats = await monitoring_service.get_active_sessions_count(24)
        required_fields = ['active_sessions', 'active_users', 'activity_rate']
        for field in required_fields:
            assert field in active_stats, f"监控数据缺少字段: {field}"
        
        # 验证恢复数据格式
        recoverable_sessions = await recovery_service.get_recoverable_sessions("test_user")
        if recoverable_sessions:
            session = recoverable_sessions[0]
            required_fields = ['session_id', 'title', 'created_at', 'can_recover']
            for field in required_fields:
                assert field in session, f"恢复会话数据缺少字段: {field}"
        
        tests_passed += 1
        print("✅ 前端API接口格式验证通过")
        
    except Exception as e:
        print(f"❌ 前端API接口格式验证失败: {e}")
    
    # === 测试4: 错误处理验证 ===
    print("\n⚠️ 测试4: 错误处理验证")
    total_tests += 1
    
    try:
        # 测试不存在会话的恢复
        try:
            await recovery_service.restore_session_context("non_existent_session")
            print("❌ 应该抛出异常但没有")
        except Exception:
            print("✅ 正确处理了不存在会话的恢复请求")
        
        # 测试无效参数的监控请求
        try:
            await monitoring_service.get_active_sessions_count(-1)
            print("✅ 处理了无效时间窗口参数")
        except Exception:
            print("✅ 正确处理了无效监控参数")
        
        tests_passed += 1
        print("✅ 错误处理验证通过")
        
    except Exception as e:
        print(f"❌ 错误处理验证失败: {e}")
    
    # === 测试5: 性能基准测试 ===
    print("\n⚡ 测试5: 性能基准测试")
    total_tests += 1
    
    try:
        import time
        
        # 测试监控API响应时间
        start_time = time.time()
        await monitoring_service.get_system_health_metrics()
        monitoring_time = time.time() - start_time
        
        # 测试恢复API响应时间
        start_time = time.time()
        await recovery_service.get_recoverable_sessions("test_user", 10)
        recovery_time = time.time() - start_time
        
        print(f"✅ 监控API响应时间: {monitoring_time:.3f}秒")
        print(f"✅ 恢复API响应时间: {recovery_time:.3f}秒")
        
        # 验证响应时间在合理范围内
        assert monitoring_time < 2.0, f"监控API响应时间过长: {monitoring_time:.3f}秒"
        assert recovery_time < 2.0, f"恢复API响应时间过长: {recovery_time:.3f}秒"
        
        tests_passed += 1
        print("✅ 性能基准测试通过")
        
    except Exception as e:
        print(f"❌ 性能基准测试失败: {e}")
    
    # === 测试结果总结 ===
    print("\n" + "=" * 60)
    print("📋 Task 4.1 前端API功能扩展测试总结:")
    print(f"✅ 通过测试: {tests_passed}/{total_tests}")
    print(f"📊 成功率: {(tests_passed/total_tests)*100:.1f}%")
    
    if tests_passed == total_tests:
        print("🎉 Task 4.1: API功能扩展 - 完成！")
        print("\n✨ 新增功能:")
        print("  • 5个监控API端点集成")
        print("  • 会话恢复功能API")
        print("  • 完整性检查API")
        print("  • 错误处理机制")
        print("  • 性能优化")
        print("\n🚀 可以进入Task 4.2：会话管理UI增强")
        return True
    else:
        print("❌ 部分测试失败，需要修复后再继续")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_frontend_api_integration())
    sys.exit(0 if success else 1)
