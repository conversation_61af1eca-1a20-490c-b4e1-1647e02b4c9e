"""
会话删除API测试 - 第二阶段任务2.2测试
测试新增的会话删除功能，包括软删除、硬删除、批量删除和恢复功能
"""

import pytest
import asyncio
import httpx
import json
from typing import Dict, Any, List

# 测试配置
API_BASE_URL = "http://localhost:8000"
TEST_SESSION_PREFIX = "test_deletion_"


class TestSessionDeletionAPI:
    """会话删除API测试类"""
    
    def __init__(self):
        self.created_sessions: List[str] = []
        self.client = None
    
    async def setup_client(self):
        """设置HTTP客户端"""
        self.client = httpx.AsyncClient(base_url=API_BASE_URL, timeout=30.0)
    
    async def cleanup_client(self):
        """清理HTTP客户端"""
        if self.client:
            await self.client.aclose()
    
    async def create_test_session(self, title_suffix: str = "") -> str:
        """创建测试会话"""
        session_data = {
            "title": f"{TEST_SESSION_PREFIX}删除测试{title_suffix}",
            "user_id": "test_deletion_user",
            "metadata": {"test": True, "purpose": "deletion_test"}
        }
        
        response = await self.client.post("/api/sessions/", json=session_data)
        assert response.status_code == 200
        
        session_id = response.json()["id"]
        self.created_sessions.append(session_id)
        return session_id
    
    async def add_test_message(self, session_id: str, content: str = "测试消息"):
        """向会话添加测试消息"""
        response = await self.client.post(
            f"/api/sessions/{session_id}/messages",
            params={
                "role": "user",
                "content": content
            }
        )
        assert response.status_code == 200
        return response.json()
    
    async def test_soft_delete_session(self):
        """测试软删除会话"""
        print("\n🧪 测试软删除会话...")
        
        # 创建测试会话
        session_id = await self.create_test_session("_软删除")
        await self.add_test_message(session_id, "这是要被软删除的会话")
        
        # 执行软删除
        response = await self.client.delete(
            f"/api/sessions/{session_id}",
            params={"reason": "测试软删除功能"}
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["session_id"] == session_id
        assert result["deleted"] == True
        assert result["deletion_type"] == "soft"
        assert result["can_recover"] == True
        assert "deletion_info" in result
        
        print(f"✅ 软删除成功: {session_id}")
        print(f"   删除类型: {result['deletion_type']}")
        print(f"   可恢复: {result['can_recover']}")
        
        return session_id
    
    async def test_hard_delete_session(self):
        """测试硬删除会话"""
        print("\n🧪 测试硬删除会话...")
        
        # 创建测试会话
        session_id = await self.create_test_session("_硬删除")
        await self.add_test_message(session_id, "这是要被硬删除的会话")
        
        # 先测试未确认的硬删除（应该失败）
        response = await self.client.delete(f"/api/sessions/{session_id}/hard")
        assert response.status_code == 400
        assert "需要明确确认" in response.json()["detail"]
        
        # 执行确认的硬删除
        response = await self.client.delete(
            f"/api/sessions/{session_id}/hard",
            params={"confirm": True}
        )
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["session_id"] == session_id
        assert result["deleted"] == True
        assert result["deletion_type"] == "hard"
        assert result["can_recover"] == False
        
        print(f"✅ 硬删除成功: {session_id}")
        print(f"   删除类型: {result['deletion_type']}")
        print(f"   可恢复: {result['can_recover']}")
        
        # 从跟踪列表中移除（已被硬删除）
        if session_id in self.created_sessions:
            self.created_sessions.remove(session_id)
        
        return session_id
    
    async def test_recover_session(self):
        """测试恢复会话"""
        print("\n🧪 测试恢复会话...")
        
        # 先软删除一个会话
        session_id = await self.test_soft_delete_session()
        
        # 恢复会话
        response = await self.client.post(f"/api/sessions/{session_id}/recover")
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["session_id"] == session_id
        assert result["recovered"] == True
        assert "recovery_time" in result
        
        print(f"✅ 恢复成功: {session_id}")
        print(f"   恢复时间: {result['recovery_time']}")
        
        # 验证会话确实被恢复
        response = await self.client.get(f"/api/sessions/{session_id}")
        assert response.status_code == 200
        session_data = response.json()
        assert session_data["id"] == session_id
        
        return session_id
    
    async def test_batch_delete_sessions(self):
        """测试批量删除会话"""
        print("\n🧪 测试批量删除会话...")
        
        # 创建多个测试会话
        session_ids = []
        for i in range(3):
            session_id = await self.create_test_session(f"_批量删除_{i}")
            await self.add_test_message(session_id, f"批量删除测试消息 {i}")
            session_ids.append(session_id)
        
        # 执行批量软删除
        batch_request = {
            "session_ids": session_ids,
            "deletion_type": "soft",
            "reason": "批量删除测试"
        }
        
        response = await self.client.post("/api/sessions/batch-delete", json=batch_request)
        
        assert response.status_code == 200
        result = response.json()
        
        assert result["total_requested"] == 3
        assert len(result["successful_deletions"]) == 3
        assert len(result["failed_deletions"]) == 0
        assert result["deletion_type"] == "soft"
        
        print(f"✅ 批量删除成功: {len(result['successful_deletions'])} 个会话")
        print(f"   成功: {len(result['successful_deletions'])}")
        print(f"   失败: {len(result['failed_deletions'])}")
        
        return session_ids
    
    async def test_get_deleted_sessions(self):
        """测试获取已删除会话列表"""
        print("\n🧪 测试获取已删除会话列表...")
        
        # 先创建并删除一些会话
        await self.test_batch_delete_sessions()
        
        # 获取已删除会话列表
        response = await self.client.get("/api/sessions/deleted")
        
        assert response.status_code == 200
        result = response.json()
        
        assert "deleted_sessions" in result
        assert "total_count" in result
        assert result["total_count"] >= 3  # 至少有我们刚删除的3个
        
        print(f"✅ 获取已删除会话成功: {result['total_count']} 个")
        
        # 验证删除的会话在列表中
        deleted_session_ids = [s["id"] for s in result["deleted_sessions"]]
        print(f"   已删除会话ID: {deleted_session_ids[:3]}...")
        
        return result
    
    async def test_error_scenarios(self):
        """测试错误场景"""
        print("\n🧪 测试错误场景...")
        
        # 测试删除不存在的会话
        fake_session_id = "fake_session_id_12345"
        response = await self.client.delete(f"/api/sessions/{fake_session_id}")
        assert response.status_code == 404
        assert "不存在" in response.json()["detail"]
        
        # 测试恢复不存在的会话
        response = await self.client.post(f"/api/sessions/{fake_session_id}/recover")
        assert response.status_code == 404
        
        # 测试重复删除
        session_id = await self.create_test_session("_重复删除测试")
        
        # 第一次删除
        response = await self.client.delete(f"/api/sessions/{session_id}")
        assert response.status_code == 200
        
        # 第二次删除（应该失败）
        response = await self.client.delete(f"/api/sessions/{session_id}")
        print(f"   重复删除响应状态码: {response.status_code}")
        if response.status_code != 400:
            print(f"   响应内容: {response.text}")
        assert response.status_code == 400
        assert "已被删除" in response.json()["detail"]
        
        print("✅ 错误场景测试通过")
    
    async def cleanup_test_sessions(self):
        """清理测试会话"""
        print("\n🧹 清理测试会话...")
        
        cleanup_count = 0
        for session_id in self.created_sessions[:]:
            try:
                # 尝试硬删除
                response = await self.client.delete(
                    f"/api/sessions/{session_id}/hard",
                    params={"confirm": True}
                )
                if response.status_code == 200:
                    cleanup_count += 1
                    self.created_sessions.remove(session_id)
            except Exception as e:
                print(f"   清理会话 {session_id} 失败: {e}")
        
        print(f"✅ 清理完成: {cleanup_count} 个会话")
    
    async def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始会话删除API测试...")
        
        try:
            await self.setup_client()
            
            # 运行各项测试
            await self.test_soft_delete_session()
            await self.test_hard_delete_session()
            await self.test_recover_session()
            await self.test_batch_delete_sessions()
            await self.test_get_deleted_sessions()
            await self.test_error_scenarios()
            
            print("\n🎉 所有删除功能测试通过！")
            
        except Exception as e:
            print(f"\n❌ 测试失败: {e}")
            raise
        finally:
            await self.cleanup_test_sessions()
            await self.cleanup_client()


async def main():
    """主测试函数"""
    tester = TestSessionDeletionAPI()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
