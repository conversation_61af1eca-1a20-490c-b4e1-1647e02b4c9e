"""
会话恢复API测试
测试会话上下文恢复、可恢复会话列表、会话完整性检查等功能
"""

import pytest
import asyncio
import json
from datetime import datetime
from fastapi.testclient import TestClient
from fastapi import FastAPI

# 导入API路由
from api.session_management import router
from services.enhanced_data_layer import EnhancedDataLayer


# 创建测试应用
app = FastAPI()
app.include_router(router)
client = TestClient(app)


class TestSessionRecoveryAPI:
    """会话恢复API测试类"""

    def __init__(self):
        """初始化测试类"""
        self.test_user_id = "test_recovery_user"
        self.test_session_ids = []

    def setup_method(self):
        """每个测试方法前的设置"""
        self.test_user_id = "test_recovery_user"
        self.test_session_ids = []

    def teardown_method(self):
        """每个测试方法后的清理"""
        # 同步清理
        import asyncio
        asyncio.run(self._cleanup_test_data())
    
    async def _cleanup_test_data(self):
        """清理测试数据"""
        try:
            # 使用删除服务清理会话
            from services.session_deletion_service_simple import SessionDeletionService
            from services.enhanced_data_layer import EnhancedDataLayer

            data_layer = EnhancedDataLayer()
            deletion_service = SessionDeletionService(data_layer)

            # 清理测试会话
            for session_id in self.test_session_ids:
                try:
                    await deletion_service.hard_delete_session(session_id)
                except:
                    pass

            # 清理测试用户（如果有删除方法的话）
            # 暂时跳过用户清理，因为数据层可能没有删除用户的方法

        except Exception as e:
            print(f"清理测试数据失败: {e}")
    
    async def _create_test_session_with_messages(self, title: str = "测试会话") -> str:
        """创建带有消息的测试会话"""
        data_layer = EnhancedDataLayer()
        
        # 创建用户
        user = await data_layer.get_user_by_identifier(self.test_user_id)
        if not user:
            user = await data_layer.create_user(
                identifier=self.test_user_id,
                display_name="测试用户",
                metadata={"test": True}
            )
        
        # 创建会话
        session_result = await data_layer.create_session(
            user_id=user["id"],
            title=title
        )

        # 处理返回值（可能是字符串ID或SessionDict）
        if isinstance(session_result, str):
            session_id = session_result
        else:
            session_id = session_result["id"]

        self.test_session_ids.append(session_id)

        # 添加测试消息
        await data_layer.add_message(
            session_id=session_id,
            role="user",
            content="你好，这是第一条测试消息",
            metadata={"test": True}
        )

        await data_layer.add_message(
            session_id=session_id,
            role="assistant",
            content="你好！我是AI助手，很高兴为您服务。",
            metadata={"test": True}
        )

        await data_layer.add_message(
            session_id=session_id,
            role="user",
            content="请帮我分析一下数据",
            metadata={"test": True}
        )

        return session_id
    
    def test_restore_session_context_success(self):
        """测试成功恢复会话上下文"""
        async def run_test():
            # 创建测试会话
            session_id = await self._create_test_session_with_messages("恢复测试会话")
            
            # 调用恢复API
            response = client.post(f"/api/sessions/{session_id}/restore")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应结构
            assert "success" in data
            assert "session_id" in data
            assert "message_count" in data
            assert "recovery_time" in data
            assert "session_info" in data
            
            # 验证会话ID正确
            assert data["session_id"] == session_id
            
            # 验证消息数量
            assert data["message_count"] >= 3  # 至少有3条测试消息
            
            print(f"✅ 会话恢复成功: {data}")
        
        asyncio.run(run_test())
    
    def test_restore_nonexistent_session(self):
        """测试恢复不存在的会话"""
        nonexistent_session_id = "nonexistent_session_12345"
        
        response = client.post(f"/api/sessions/{nonexistent_session_id}/restore")
        
        assert response.status_code == 200  # API不抛异常，返回错误信息
        data = response.json()
        
        assert data["success"] is False
        assert "error" in data
        assert "不存在" in data["error"]
        
        print(f"✅ 不存在会话处理正确: {data}")
    
    def test_get_recoverable_sessions(self):
        """测试获取可恢复会话列表"""
        async def run_test():
            # 创建多个测试会话
            session1_id = await self._create_test_session_with_messages("可恢复会话1")
            session2_id = await self._create_test_session_with_messages("可恢复会话2")
            
            # 调用API
            response = client.get(f"/api/sessions/recoverable/{self.test_user_id}")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应结构
            assert "user_id" in data
            assert "sessions" in data
            assert "total_sessions" in data
            
            # 验证用户ID
            assert data["user_id"] == self.test_user_id
            
            # 验证会话数量
            assert data["total_sessions"] >= 2
            
            # 验证会话信息
            sessions = data["sessions"]
            assert len(sessions) >= 2
            
            for session in sessions:
                assert "session_id" in session
                assert "title" in session
                assert "message_count" in session
                assert "can_recover" in session
                assert session["can_recover"] is True  # 有消息的会话应该可以恢复
            
            print(f"✅ 可恢复会话列表获取成功: {data}")
        
        asyncio.run(run_test())
    
    def test_get_recoverable_sessions_with_limit(self):
        """测试带限制的可恢复会话列表"""
        async def run_test():
            # 创建多个测试会话
            for i in range(5):
                await self._create_test_session_with_messages(f"限制测试会话{i+1}")
            
            # 调用API，限制返回3个
            response = client.get(f"/api/sessions/recoverable/{self.test_user_id}?limit=3")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证限制生效
            assert len(data["sessions"]) <= 3
            
            print(f"✅ 限制功能正常: 返回{len(data['sessions'])}个会话")
        
        asyncio.run(run_test())
    
    def test_check_session_integrity(self):
        """测试会话完整性检查"""
        async def run_test():
            # 创建测试会话
            session_id = await self._create_test_session_with_messages("完整性测试会话")
            
            # 调用完整性检查API
            response = client.get(f"/api/sessions/{session_id}/integrity")
            
            assert response.status_code == 200
            data = response.json()
            
            # 验证响应结构
            assert "valid" in data
            assert "session_id" in data
            assert "metadata_exists" in data
            assert "message_count" in data
            
            # 验证会话有效
            assert data["valid"] is True
            assert data["session_id"] == session_id
            assert data["metadata_exists"] is True
            assert data["message_count"] >= 3
            
            print(f"✅ 会话完整性检查通过: {data}")
        
        asyncio.run(run_test())
    
    def test_check_integrity_nonexistent_session(self):
        """测试检查不存在会话的完整性"""
        nonexistent_session_id = "nonexistent_integrity_12345"
        
        response = client.get(f"/api/sessions/{nonexistent_session_id}/integrity")
        
        assert response.status_code == 200
        data = response.json()
        
        assert data["valid"] is False
        assert "error" in data
        
        print(f"✅ 不存在会话完整性检查正确: {data}")
    
    def test_recovery_workflow(self):
        """测试完整的恢复工作流程"""
        async def run_test():
            # 1. 创建会话
            session_id = await self._create_test_session_with_messages("工作流测试会话")
            
            # 2. 检查完整性
            integrity_response = client.get(f"/api/sessions/{session_id}/integrity")
            assert integrity_response.status_code == 200
            integrity_data = integrity_response.json()
            assert integrity_data["valid"] is True
            
            # 3. 获取可恢复会话列表
            recoverable_response = client.get(f"/api/sessions/recoverable/{self.test_user_id}")
            assert recoverable_response.status_code == 200
            recoverable_data = recoverable_response.json()
            
            # 验证会话在可恢复列表中
            session_found = False
            for session in recoverable_data["sessions"]:
                if session["session_id"] == session_id:
                    session_found = True
                    assert session["can_recover"] is True
                    break
            assert session_found, "会话应该在可恢复列表中"
            
            # 4. 恢复会话上下文
            restore_response = client.post(f"/api/sessions/{session_id}/restore")
            assert restore_response.status_code == 200
            restore_data = restore_response.json()
            assert restore_data["success"] is True
            
            print(f"✅ 完整恢复工作流程测试通过")
            print(f"   - 完整性检查: {integrity_data['valid']}")
            print(f"   - 可恢复: {session_found}")
            print(f"   - 恢复成功: {restore_data['success']}")
        
        asyncio.run(run_test())


if __name__ == "__main__":
    # 运行测试
    test_instance = TestSessionRecoveryAPI()
    
    print("🧪 开始会话恢复API测试...")
    
    # 运行各个测试
    test_methods = [
        "test_restore_session_context_success",
        "test_restore_nonexistent_session", 
        "test_get_recoverable_sessions",
        "test_get_recoverable_sessions_with_limit",
        "test_check_session_integrity",
        "test_check_integrity_nonexistent_session",
        "test_recovery_workflow"
    ]
    
    for method_name in test_methods:
        print(f"\n📋 运行测试: {method_name}")
        try:
            method = getattr(test_instance, method_name)
            method()
            print(f"✅ {method_name} 通过")
        except Exception as e:
            print(f"❌ {method_name} 失败: {e}")
    
    print("\n🎉 会话恢复API测试完成！")
