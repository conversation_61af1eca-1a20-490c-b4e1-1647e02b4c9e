#!/usr/bin/env python3
"""
WebSocket聊天功能测试脚本
测试修复后的AI响应功能
"""

import asyncio
import websockets
import json
import sys
from datetime import datetime

async def test_chat_functionality():
    """测试完整的聊天功能"""
    print("🧪 开始测试WebSocket聊天功能...")
    
    try:
        # 连接WebSocket
        uri = 'ws://localhost:8000/ws/test_chat_session'
        print(f"📡 连接到: {uri}")
        
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket连接成功!")
            
            # 等待连接确认
            response = await websocket.recv()
            connection_data = json.loads(response)
            print(f"🔗 连接确认: {connection_data.get('message')}")
            
            # 测试多轮对话
            test_messages = [
                "你好，请介绍一下你自己",
                "你能做什么？",
                "请帮我写一个Python函数来计算斐波那契数列"
            ]
            
            for i, message in enumerate(test_messages, 1):
                print(f"\n💬 第{i}轮对话:")
                print(f"👤 用户: {message}")
                
                # 发送消息
                chat_message = {
                    'type': 'chat',
                    'message': message,
                    'thread_id': 'test_chat_session',
                    'user_id': 'test_user'
                }
                
                await websocket.send(json.dumps(chat_message))
                
                # 接收AI响应
                ai_response = ""
                response_count = 0
                
                while True:
                    try:
                        response = await asyncio.wait_for(websocket.recv(), timeout=15.0)
                        data = json.loads(response)
                        response_count += 1
                        
                        msg_type = data.get('type')
                        status = data.get('status')
                        content = data.get('content', '')
                        thinking = data.get('thinking', False)
                        
                        if msg_type == 'AIMessage':
                            if thinking:
                                print("🤔 AI正在思考...")
                            elif status == 'responding' and content:
                                ai_response = content
                                print(f"💭 AI响应中... (长度: {len(content)})")
                            elif status == 'complete':
                                if content:
                                    ai_response = content
                                print(f"🤖 AI: {ai_response[:100]}{'...' if len(ai_response) > 100 else ''}")
                                break
                            elif status == 'error':
                                print(f"❌ AI响应错误: {content}")
                                break
                        
                        # 防止无限循环
                        if response_count > 10:
                            print("⚠️ 响应次数过多，停止接收")
                            break
                            
                    except asyncio.TimeoutError:
                        print("⏰ 等待AI响应超时")
                        break
                
                # 短暂等待
                await asyncio.sleep(1)
            
            print(f"\n✅ 聊天功能测试完成!")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return True

async def test_session_persistence():
    """测试会话持久化"""
    print("\n🗄️ 测试会话持久化...")
    
    try:
        # 测试API端点
        import aiohttp
        
        async with aiohttp.ClientSession() as session:
            # 获取会话列表
            async with session.get('http://localhost:8000/api/sessions/list?user_id=test_user') as resp:
                if resp.status == 200:
                    sessions = await resp.json()
                    print(f"📋 找到 {len(sessions)} 个会话")
                    
                    for s in sessions[:3]:  # 显示前3个会话
                        print(f"  - {s.get('title', '无标题')} (消息数: {s.get('message_count', 0)})")
                else:
                    print(f"❌ 获取会话列表失败: {resp.status}")
                    
    except Exception as e:
        print(f"❌ 会话持久化测试失败: {e}")

async def main():
    """主测试函数"""
    print("🚀 启动WebSocket聊天功能综合测试")
    print("=" * 50)
    
    # 测试聊天功能
    chat_success = await test_chat_functionality()
    
    # 测试会话持久化
    await test_session_persistence()
    
    print("\n" + "=" * 50)
    if chat_success:
        print("🎉 所有测试通过！AI聊天功能正常工作")
    else:
        print("💥 测试失败，需要进一步调试")
    
    return chat_success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
        sys.exit(1)
