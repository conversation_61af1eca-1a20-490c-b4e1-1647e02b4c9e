#!/usr/bin/env python3
"""
Task 4.2 会话管理UI增强测试
测试增强的会话管理组件和相关功能
"""

import asyncio
import sys
import os
import json
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.enhanced_data_layer import EnhancedDataLayer

async def test_enhanced_session_manager_data():
    """测试增强会话管理器所需的数据接口"""
    print("🧪 测试 Task 4.2: 会话管理UI增强")
    print("=" * 60)
    
    # 初始化数据层
    data_layer = EnhancedDataLayer()
    
    try:
        # 1. 创建测试数据
        print("\n1. 创建测试会话数据...")
        test_user_id = "test_user_ui"
        test_sessions = []
        
        # 创建多个测试会话
        for i in range(5):
            session_result = await data_layer.create_session(
                user_id=test_user_id,
                title=f"测试会话 {i+1}",
                metadata={
                    "mode": "single_agent",
                    "test_session": True,
                    "priority": "normal" if i % 2 == 0 else "high"
                }
            )
            test_sessions.append(session_result["id"])
            print(f"   ✅ 创建会话: {session_result['id']}")
            
            # 为每个会话添加一些消息
            for j in range(i + 1):
                await data_layer.add_message(
                    session_id=session_result["id"],
                    message_type="human",
                    content=f"测试消息 {j+1} 来自会话 {i+1}",
                    metadata={"test": True}
                )
        
        # 2. 测试会话列表获取
        print("\n2. 测试会话列表获取...")
        sessions_response = await data_layer.get_sessions(user_id=test_user_id)
        print(f"   ✅ 获取到 {len(sessions_response['sessions'])} 个会话")
        
        # 验证会话数据结构
        for session in sessions_response['sessions']:
            required_fields = ['id', 'title', 'created_at', 'user_id']
            for field in required_fields:
                assert field in session, f"会话缺少必需字段: {field}"
            print(f"   ✅ 会话 {session['id'][:8]}... 数据结构正确")
        
        # 3. 测试会话消息获取
        print("\n3. 测试会话消息获取...")
        test_session_id = test_sessions[0]
        messages = await data_layer.get_session_messages(test_session_id)
        print(f"   ✅ 会话 {test_session_id[:8]}... 有 {len(messages)} 条消息")
        
        # 4. 测试会话重命名功能
        print("\n4. 测试会话重命名功能...")
        new_title = f"重命名的会话 - {datetime.now().strftime('%H:%M:%S')}"
        await data_layer.update_session(
            session_id=test_session_id,
            title=new_title
        )
        
        # 验证重命名
        updated_sessions = await data_layer.get_sessions(user_id=test_user_id)
        updated_session = next(s for s in updated_sessions['sessions'] if s['id'] == test_session_id)
        assert updated_session['title'] == new_title, "会话重命名失败"
        print(f"   ✅ 会话重命名成功: {new_title}")
        
        # 5. 测试会话删除功能
        print("\n5. 测试会话删除功能...")
        session_to_delete = test_sessions[-1]
        await data_layer.delete_session(session_to_delete)
        
        # 验证删除
        remaining_sessions = await data_layer.get_sessions(user_id=test_user_id)
        remaining_ids = [s['id'] for s in remaining_sessions['sessions']]
        assert session_to_delete not in remaining_ids, "会话删除失败"
        print(f"   ✅ 会话删除成功: {session_to_delete[:8]}...")
        
        # 6. 测试会话统计信息
        print("\n6. 测试会话统计信息...")
        stats = await data_layer.get_session_stats(user_id=test_user_id)
        print(f"   ✅ 用户统计: {stats}")
        
        # 验证统计数据结构
        expected_stats = ['total_sessions', 'total_messages', 'avg_session_length']
        for stat in expected_stats:
            assert stat in stats, f"统计数据缺少字段: {stat}"
        
        # 7. 测试会话搜索功能
        print("\n7. 测试会话搜索功能...")
        search_results = await data_layer.search_sessions(
            user_id=test_user_id,
            query="测试",
            limit=10
        )
        print(f"   ✅ 搜索到 {len(search_results)} 个匹配会话")
        
        # 8. 测试批量操作数据准备
        print("\n8. 测试批量操作数据准备...")
        
        # 获取剩余会话ID用于批量测试
        remaining_sessions = await data_layer.get_sessions(user_id=test_user_id)
        remaining_session_ids = [s['id'] for s in remaining_sessions['sessions']]
        
        print(f"   ✅ 准备批量操作，剩余会话: {len(remaining_session_ids)} 个")
        
        # 模拟导出数据结构
        export_data = {
            "sessions": [],
            "exportedAt": datetime.now().isoformat(),
            "totalSessions": len(remaining_session_ids)
        }
        
        for session_id in remaining_session_ids[:2]:  # 只导出前2个作为测试
            session_data = next(s for s in remaining_sessions['sessions'] if s['id'] == session_id)
            messages = await data_layer.get_session_messages(session_id)
            
            export_data["sessions"].append({
                "session": {
                    "id": session_data['id'],
                    "title": session_data['title'],
                    "createdAt": session_data['created_at'],
                    "messageCount": len(messages)
                },
                "messages": messages
            })
        
        print(f"   ✅ 导出数据结构准备完成，包含 {len(export_data['sessions'])} 个会话")
        
        # 9. 测试会话活跃度数据
        print("\n9. 测试会话活跃度数据...")
        
        # 更新一些会话的最后活跃时间
        for i, session_id in enumerate(remaining_session_ids[:3]):
            # 模拟不同的活跃时间
            last_activity = datetime.now() - timedelta(hours=i*2)
            await data_layer.update_session_activity(session_id, last_activity)
            print(f"   ✅ 更新会话 {session_id[:8]}... 活跃时间")
        
        # 10. 清理测试数据
        print("\n10. 清理测试数据...")
        cleanup_count = 0
        for session_id in remaining_session_ids:
            try:
                await data_layer.delete_session(session_id)
                cleanup_count += 1
            except Exception as e:
                print(f"   ⚠️ 清理会话失败 {session_id[:8]}...: {e}")
        
        print(f"   ✅ 清理完成，删除了 {cleanup_count} 个测试会话")
        
        print("\n" + "=" * 60)
        print("🎉 Task 4.2 会话管理UI增强测试完成!")
        print("\n✅ 测试结果:")
        print("   - 会话CRUD操作: 正常")
        print("   - 会话消息获取: 正常") 
        print("   - 会话重命名: 正常")
        print("   - 会话删除: 正常")
        print("   - 会话统计: 正常")
        print("   - 会话搜索: 正常")
        print("   - 批量操作数据: 正常")
        print("   - 活跃度数据: 正常")
        print("   - 数据清理: 正常")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_ui_component_data_flow():
    """测试UI组件数据流"""
    print("\n" + "=" * 60)
    print("🧪 测试UI组件数据流")
    print("=" * 60)
    
    try:
        # 模拟前端组件需要的数据结构
        print("\n1. 模拟EnhancedSessionManager组件数据需求...")
        
        # 模拟会话列表数据
        mock_sessions = [
            {
                "id": "session_1",
                "title": "AI助手对话",
                "createdAt": datetime.now() - timedelta(hours=2),
                "lastActiveTime": datetime.now() - timedelta(minutes=30),
                "messageCount": 15,
                "userId": "user_1",
                "metadata": {"mode": "single_agent", "priority": "high"}
            },
            {
                "id": "session_2", 
                "title": "技术咨询",
                "createdAt": datetime.now() - timedelta(days=1),
                "lastActiveTime": datetime.now() - timedelta(hours=5),
                "messageCount": 8,
                "userId": "user_1",
                "metadata": {"mode": "multi_agent", "priority": "normal"}
            }
        ]
        
        # 测试搜索过滤逻辑
        search_keyword = "AI"
        filtered_sessions = [
            session for session in mock_sessions 
            if search_keyword.lower() in session["title"].lower()
        ]
        print(f"   ✅ 搜索 '{search_keyword}' 找到 {len(filtered_sessions)} 个会话")
        
        # 测试排序逻辑
        sorted_by_activity = sorted(mock_sessions, key=lambda x: x["lastActiveTime"], reverse=True)
        sorted_by_messages = sorted(mock_sessions, key=lambda x: x["messageCount"], reverse=True)
        
        print(f"   ✅ 按活跃时间排序: {[s['title'] for s in sorted_by_activity]}")
        print(f"   ✅ 按消息数排序: {[s['title'] for s in sorted_by_messages]}")
        
        # 测试分页逻辑
        page_size = 1
        total_pages = (len(mock_sessions) + page_size - 1) // page_size
        page_1 = mock_sessions[0:page_size]
        
        print(f"   ✅ 分页测试: 总页数 {total_pages}, 第1页有 {len(page_1)} 个会话")
        
        # 2. 模拟SessionRecoveryPanel组件数据需求
        print("\n2. 模拟SessionRecoveryPanel组件数据需求...")
        
        mock_recoverable_sessions = [
            {
                "session_id": "broken_session_1",
                "title": "损坏的会话",
                "created_at": datetime.now() - timedelta(days=2),
                "message_count": 5,
                "metadata_exists": True,
                "state_consistent": False,
                "can_recover": True,
                "error": "状态不一致"
            }
        ]
        
        # 测试恢复进度数据
        mock_recovery_progress = {
            "broken_session_1": {
                "status": "restoring",
                "progress": 65,
                "message": "正在恢复会话状态...",
                "timestamp": datetime.now()
            }
        }
        
        print(f"   ✅ 可恢复会话: {len(mock_recoverable_sessions)} 个")
        print(f"   ✅ 恢复进度: {mock_recovery_progress['broken_session_1']['progress']}%")
        
        # 3. 模拟MonitoringDashboard组件数据需求
        print("\n3. 模拟MonitoringDashboard组件数据需求...")
        
        mock_monitoring_data = {
            "activeSessionsStats": {
                "active_sessions": 12,
                "active_users": 8,
                "activity_rate": 75.5
            },
            "sessionDurationAnalysis": {
                "average_duration": 1800,  # 30分钟
                "max_duration": 7200,      # 2小时
                "total_sessions": 45
            },
            "messageFrequencyStats": {
                "total_messages": 1250,
                "average_per_day": 85,
                "peak_hour": 14
            },
            "userActivityAnalysis": {
                "top_users": [
                    {
                        "user_id": "user_1",
                        "session_count": 15,
                        "message_count": 120,
                        "activity_score": 95
                    }
                ],
                "max_sessions": 15
            },
            "systemHealthMetrics": {
                "status": "healthy",
                "database_size": 1024 * 1024 * 50,  # 50MB
                "total_sessions": 150,
                "total_users": 25,
                "uptime": 86400 * 7  # 7天
            },
            "lastUpdated": datetime.now().isoformat()
        }
        
        print(f"   ✅ 活跃会话: {mock_monitoring_data['activeSessionsStats']['active_sessions']}")
        print(f"   ✅ 系统状态: {mock_monitoring_data['systemHealthMetrics']['status']}")
        print(f"   ✅ 数据库大小: {mock_monitoring_data['systemHealthMetrics']['database_size'] / (1024*1024):.1f}MB")
        
        # 4. 测试数据格式化函数
        print("\n4. 测试数据格式化函数...")
        
        def format_duration(seconds):
            if seconds < 60:
                return f"{seconds}秒"
            elif seconds < 3600:
                return f"{seconds // 60}分钟"
            else:
                return f"{seconds // 3600}小时"
        
        def format_file_size(bytes_size):
            if bytes_size < 1024:
                return f"{bytes_size} B"
            elif bytes_size < 1024 * 1024:
                return f"{bytes_size / 1024:.1f} KB"
            else:
                return f"{bytes_size / (1024 * 1024):.1f} MB"
        
        def format_relative_time(date_time):
            now = datetime.now()
            diff = now - date_time
            hours = diff.total_seconds() / 3600
            
            if hours < 1:
                return "刚刚"
            elif hours < 24:
                return f"{int(hours)} 小时前"
            else:
                days = int(hours / 24)
                return f"{days} 天前"
        
        # 测试格式化函数
        test_duration = 1800
        test_file_size = 1024 * 1024 * 50
        test_time = datetime.now() - timedelta(hours=3)
        
        print(f"   ✅ 时长格式化: {test_duration}秒 -> {format_duration(test_duration)}")
        print(f"   ✅ 文件大小格式化: {test_file_size}字节 -> {format_file_size(test_file_size)}")
        print(f"   ✅ 相对时间格式化: {format_relative_time(test_time)}")
        
        print("\n" + "=" * 60)
        print("🎉 UI组件数据流测试完成!")
        
        return True
        
    except Exception as e:
        print(f"\n❌ UI组件数据流测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始 Task 4.2 会话管理UI增强 完整测试")
    print("=" * 80)
    
    # 运行所有测试
    test_results = []
    
    # 测试1: 增强会话管理器数据接口
    result1 = await test_enhanced_session_manager_data()
    test_results.append(("会话管理数据接口", result1))
    
    # 测试2: UI组件数据流
    result2 = await test_ui_component_data_flow()
    test_results.append(("UI组件数据流", result2))
    
    # 输出总结
    print("\n" + "=" * 80)
    print("📊 Task 4.2 测试总结")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 Task 4.2 会话管理UI增强测试全部通过!")
        print("\n✅ 功能验证:")
        print("   - EnhancedSessionManager 组件数据支持完整")
        print("   - SessionRecoveryPanel 组件数据支持完整") 
        print("   - MonitoringDashboard 组件数据支持完整")
        print("   - 所有UI组件的数据流测试通过")
        print("   - 数据格式化函数工作正常")
        return True
    else:
        print("❌ 部分测试失败，请检查相关功能")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
