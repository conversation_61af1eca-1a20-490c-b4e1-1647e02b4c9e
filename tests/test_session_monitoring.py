"""
会话监控服务测试
测试会话状态监控和统计功能
"""

import asyncio
import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_data_layer import EnhancedDataLayer
from services.session_monitoring_service import SessionMonitoringService


async def test_monitoring_service():
    """测试监控服务功能"""
    print("🚀 开始会话监控功能测试\n")
    
    # 初始化服务
    data_layer = EnhancedDataLayer("./data/test_monitoring.db")
    monitoring_service = SessionMonitoringService(data_layer)
    
    try:
        # 创建测试数据
        print("📝 创建测试数据...")
        
        # 创建测试用户
        user1 = await data_layer.create_user(
            identifier="test_user_1",
            display_name="测试用户1",
            metadata={"test": True}
        )
        
        user2 = await data_layer.create_user(
            identifier="test_user_2", 
            display_name="测试用户2",
            metadata={"test": True}
        )
        
        print(f"✅ 创建用户: {user1['id']}, {user2['id']}")
        
        # 创建测试会话
        session1 = await data_layer.create_session(
            user_id=user1['id'],
            title="测试会话1",
            metadata={"test": True}
        )
        
        session2 = await data_layer.create_session(
            user_id=user2['id'],
            title="测试会话2", 
            metadata={"test": True}
        )
        
        session3 = await data_layer.create_session(
            user_id=user1['id'],
            title="测试会话3",
            metadata={"test": True}
        )
        
        print(f"✅ 创建会话: {session1['id'][:8]}..., {session2['id'][:8]}..., {session3['id'][:8]}...")
        
        # 添加测试消息
        messages_data = [
            (session1['id'], "user", "你好，这是第一条消息"),
            (session1['id'], "assistant", "你好！我是AI助手"),
            (session1['id'], "user", "请帮我解决一个问题"),
            (session1['id'], "assistant", "当然可以，请告诉我具体问题"),
            
            (session2['id'], "user", "测试消息1"),
            (session2['id'], "assistant", "收到测试消息"),
            
            (session3['id'], "user", "另一个会话的消息"),
            (session3['id'], "assistant", "这是另一个会话的回复"),
            (session3['id'], "user", "继续对话"),
        ]
        
        for session_id, role, content in messages_data:
            await data_layer.add_message(
                session_id=session_id,
                role=role,
                content=content,
                metadata={"test": True}
            )
        
        print(f"✅ 添加了 {len(messages_data)} 条测试消息")
        
        print("\n" + "="*50)
        print("🧪 开始测试监控功能...")
        
        # 测试1: 活跃会话统计
        print("\n📊 测试活跃会话统计...")
        active_stats = await monitoring_service.get_active_sessions_count(24)
        print(f"✅ 活跃会话统计:")
        print(f"   - 活跃会话数: {active_stats.get('active_sessions', 0)}")
        print(f"   - 活跃用户数: {active_stats.get('active_users', 0)}")
        print(f"   - 总会话数: {active_stats.get('total_sessions', 0)}")
        print(f"   - 活跃率: {active_stats.get('activity_rate', 0)}%")
        
        # 测试2: 会话持续时间分析
        print("\n⏱️ 测试会话持续时间分析...")
        duration_analysis = await monitoring_service.get_session_duration_analysis(10)
        print(f"✅ 会话持续时间分析:")
        print(f"   - 分析会话数: {duration_analysis.get('total_analyzed_sessions', 0)}")
        print(f"   - 有消息的会话数: {duration_analysis.get('active_sessions_with_messages', 0)}")
        print(f"   - 平均持续时间: {duration_analysis.get('average_duration_minutes', 0)} 分钟")
        print(f"   - 总持续时间: {duration_analysis.get('total_duration_hours', 0)} 小时")
        
        longest_sessions = duration_analysis.get('longest_sessions', [])
        if longest_sessions:
            print("   - 最长会话:")
            for i, session in enumerate(longest_sessions[:3]):
                print(f"     {i+1}. {session.get('title', 'N/A')[:30]} - {session.get('duration_minutes', 0)} 分钟")
        
        # 测试3: 消息频率统计
        print("\n📈 测试消息频率统计...")
        frequency_stats = await monitoring_service.get_message_frequency_stats(7)
        print(f"✅ 消息频率统计:")
        print(f"   - 分析周期: {frequency_stats.get('analysis_period_days', 0)} 天")
        print(f"   - 总消息数: {frequency_stats.get('total_messages', 0)}")
        print(f"   - 日均消息数: {frequency_stats.get('average_daily_messages', 0)}")
        
        daily_stats = frequency_stats.get('daily_statistics', [])
        if daily_stats:
            print("   - 每日统计:")
            for day in daily_stats[:3]:
                print(f"     {day.get('date')}: {day.get('message_count')} 条消息, {day.get('active_sessions')} 个活跃会话")
        
        # 测试4: 用户活跃度分析
        print("\n👥 测试用户活跃度分析...")
        user_analysis = await monitoring_service.get_user_activity_analysis(10)
        print(f"✅ 用户活跃度分析:")
        print(f"   - 分析用户数: {user_analysis.get('total_analyzed_users', 0)}")
        print(f"   - 总会话数: {user_analysis.get('total_sessions', 0)}")
        print(f"   - 总用户消息数: {user_analysis.get('total_user_messages', 0)}")
        print(f"   - 人均会话数: {user_analysis.get('average_sessions_per_user', 0)}")
        print(f"   - 人均消息数: {user_analysis.get('average_messages_per_user', 0)}")
        
        top_users = user_analysis.get('top_users', [])
        if top_users:
            print("   - 最活跃用户:")
            for i, user in enumerate(top_users[:3]):
                print(f"     {i+1}. {user.get('display_name', 'N/A')} - {user.get('message_count')} 条消息, {user.get('session_count')} 个会话")
        
        # 测试5: 系统健康指标
        print("\n🏥 测试系统健康指标...")
        health_metrics = await monitoring_service.get_system_health_metrics()
        print(f"✅ 系统健康指标:")
        print(f"   - 数据库大小: {health_metrics.get('database_size_mb', 0)} MB")
        print(f"   - 系统状态: {health_metrics.get('system_status', 'unknown')}")
        
        recent_activity = health_metrics.get('recent_activity', {})
        print(f"   - 最近1小时会话: {recent_activity.get('sessions_last_hour', 0)}")
        print(f"   - 最近1小时消息: {recent_activity.get('messages_last_hour', 0)}")
        print(f"   - 最近24小时会话: {recent_activity.get('sessions_last_24h', 0)}")
        print(f"   - 最近24小时消息: {recent_activity.get('messages_last_24h', 0)}")
        
        tables_status = health_metrics.get('tables_status', {})
        print(f"   - 数据表状态: {tables_status}")
        
        print("\n🎉 所有监控功能测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        try:
            # 删除测试数据库文件
            if os.path.exists("./data/test_monitoring.db"):
                os.remove("./data/test_monitoring.db")
                print("✅ 测试数据库已删除")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")
        
        print("\n✨ 监控功能测试完成！")


if __name__ == "__main__":
    asyncio.run(test_monitoring_service())
