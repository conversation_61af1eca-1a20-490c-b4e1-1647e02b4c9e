"""
监控API端点测试
测试会话监控相关的API端点
"""

import asyncio
import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from fastapi.testclient import TestClient
from api.session_management import router
from services.enhanced_data_layer import EnhancedDataLayer
from fastapi import FastAPI


async def test_monitoring_api():
    """测试监控API端点"""
    print("🚀 开始监控API测试\n")
    
    # 创建FastAPI应用和测试客户端
    app = FastAPI()
    app.include_router(router)
    client = TestClient(app)
    
    # 创建测试数据
    print("📝 创建测试数据...")
    data_layer = EnhancedDataLayer("./data/test_monitoring_api.db")
    
    try:
        # 创建测试用户和会话
        user1 = await data_layer.create_user(
            identifier="api_test_user_1",
            display_name="API测试用户1"
        )
        
        session1 = await data_layer.create_session(
            user_id=user1['id'],
            title="API测试会话1"
        )
        
        # 添加一些测试消息
        await data_layer.add_message(session1['id'], "user", "测试消息1")
        await data_layer.add_message(session1['id'], "assistant", "测试回复1")
        await data_layer.add_message(session1['id'], "user", "测试消息2")
        
        print("✅ 测试数据创建完成")
        
        print("\n" + "="*50)
        print("🧪 开始测试监控API端点...")
        
        # 测试1: 活跃会话统计API
        print("\n📊 测试活跃会话统计API...")
        response = client.get("/api/sessions/monitoring/active-sessions?time_window_hours=24")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 活跃会话统计API测试通过")
            print(f"   - 成功: {data.get('success', False)}")
            stats = data.get('data', {})
            print(f"   - 活跃会话数: {stats.get('active_sessions', 0)}")
            print(f"   - 活跃用户数: {stats.get('active_users', 0)}")
        else:
            print(f"❌ 活跃会话统计API测试失败: {response.text}")
        
        # 测试2: 会话持续时间分析API
        print("\n⏱️ 测试会话持续时间分析API...")
        response = client.get("/api/sessions/monitoring/session-duration?limit=10")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 会话持续时间分析API测试通过")
            print(f"   - 成功: {data.get('success', False)}")
            analysis = data.get('data', {})
            print(f"   - 分析会话数: {analysis.get('total_analyzed_sessions', 0)}")
        else:
            print(f"❌ 会话持续时间分析API测试失败: {response.text}")
        
        # 测试3: 消息频率统计API
        print("\n📈 测试消息频率统计API...")
        response = client.get("/api/sessions/monitoring/message-frequency?days=7")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 消息频率统计API测试通过")
            print(f"   - 成功: {data.get('success', False)}")
            stats = data.get('data', {})
            print(f"   - 总消息数: {stats.get('total_messages', 0)}")
        else:
            print(f"❌ 消息频率统计API测试失败: {response.text}")
        
        # 测试4: 用户活跃度分析API
        print("\n👥 测试用户活跃度分析API...")
        response = client.get("/api/sessions/monitoring/user-activity?limit=10")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 用户活跃度分析API测试通过")
            print(f"   - 成功: {data.get('success', False)}")
            analysis = data.get('data', {})
            print(f"   - 分析用户数: {analysis.get('total_analyzed_users', 0)}")
        else:
            print(f"❌ 用户活跃度分析API测试失败: {response.text}")
        
        # 测试5: 系统健康指标API
        print("\n🏥 测试系统健康指标API...")
        response = client.get("/api/sessions/monitoring/system-health")
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 系统健康指标API测试通过")
            print(f"   - 成功: {data.get('success', False)}")
            metrics = data.get('data', {})
            print(f"   - 系统状态: {metrics.get('system_status', 'unknown')}")
            print(f"   - 数据库大小: {metrics.get('database_size_mb', 0)} MB")
        else:
            print(f"❌ 系统健康指标API测试失败: {response.text}")
        
        print("\n🎉 所有监控API测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 清理测试数据
        print("\n🧹 清理测试数据...")
        try:
            if os.path.exists("./data/test_monitoring_api.db"):
                os.remove("./data/test_monitoring_api.db")
                print("✅ 测试数据库已删除")
        except Exception as e:
            print(f"⚠️ 清理失败: {e}")
        
        print("\n✨ 监控API测试完成！")


def test_monitoring_api_sync():
    """同步版本的API测试"""
    print("🚀 开始监控API同步测试\n")
    
    # 创建FastAPI应用和测试客户端
    app = FastAPI()
    app.include_router(router)
    client = TestClient(app)
    
    print("📊 测试监控API端点（无数据）...")
    
    # 测试所有端点是否能正常响应（即使没有数据）
    endpoints = [
        ("/api/sessions/monitoring/active-sessions", "活跃会话统计"),
        ("/api/sessions/monitoring/session-duration", "会话持续时间分析"),
        ("/api/sessions/monitoring/message-frequency", "消息频率统计"),
        ("/api/sessions/monitoring/user-activity", "用户活跃度分析"),
        ("/api/sessions/monitoring/system-health", "系统健康指标")
    ]
    
    all_passed = True
    
    for endpoint, name in endpoints:
        try:
            response = client.get(endpoint)
            if response.status_code == 200:
                data = response.json()
                success = data.get('success', False)
                print(f"✅ {name} API: 状态码 {response.status_code}, 成功: {success}")
            else:
                print(f"❌ {name} API: 状态码 {response.status_code}")
                print(f"   错误: {response.text}")
                all_passed = False
        except Exception as e:
            print(f"❌ {name} API: 异常 {e}")
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有监控API端点测试通过！")
    else:
        print("\n⚠️ 部分监控API端点测试失败")
    
    print("\n✨ 监控API同步测试完成！")


if __name__ == "__main__":
    # 运行同步测试（不需要数据）
    test_monitoring_api_sync()
    
    # 运行异步测试（需要创建数据）
    print("\n" + "="*60)
    asyncio.run(test_monitoring_api())
