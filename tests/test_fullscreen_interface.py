#!/usr/bin/env python3
"""
全屏界面测试脚本
测试新的MateChat风格全屏界面是否正确实现
"""

import requests
import time
import json
from typing import Dict, Any

def test_backend_health():
    """测试后端健康状态"""
    try:
        response = requests.get("http://localhost:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ 后端服务正常运行")
            print(f"   版本: {data.get('version', 'unknown')}")
            print(f"   状态: {data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ 后端服务异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 后端服务连接失败: {e}")
        return False

def test_frontend_accessibility():
    """测试前端页面可访问性"""
    try:
        response = requests.get("http://localhost:3001", timeout=5)
        if response.status_code == 200:
            print("✅ 前端页面可访问")
            
            # 检查关键内容
            content = response.text
            checks = [
                ("MateChat", "页面标题"),
                ("对话历史", "侧边栏标题"),
                ("猜你想问", "快速问题区域"),
                ("请输入您的问题", "输入框占位符"),
                ("发送", "发送按钮"),
                ("内容由AI生成", "免责声明")
            ]
            
            for keyword, description in checks:
                if keyword in content:
                    print(f"   ✅ {description}: 找到 '{keyword}'")
                else:
                    print(f"   ⚠️  {description}: 未找到 '{keyword}'")
            
            return True
        else:
            print(f"❌ 前端页面异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 前端页面连接失败: {e}")
        return False

def test_session_api():
    """测试会话管理API"""
    try:
        # 测试获取会话列表
        response = requests.get("http://localhost:8000/api/sessions", timeout=5)
        if response.status_code == 200:
            sessions = response.json()
            print(f"✅ 会话列表API正常 (当前会话数: {len(sessions)})")
            
            # 测试创建新会话
            create_response = requests.post("http://localhost:8000/api/sessions", timeout=5)
            if create_response.status_code == 200:
                new_session = create_response.json()
                print(f"✅ 创建会话API正常 (新会话ID: {new_session.get('session_id', 'unknown')})")
                return True
            else:
                print(f"❌ 创建会话API异常: HTTP {create_response.status_code}")
                return False
        else:
            print(f"❌ 会话列表API异常: HTTP {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 会话API测试失败: {e}")
        return False

def test_chat_api():
    """测试聊天API"""
    try:
        # 先创建一个会话
        session_response = requests.post("http://localhost:8000/api/sessions", timeout=5)
        if session_response.status_code != 200:
            print("❌ 无法创建测试会话")
            return False
        
        session_id = session_response.json().get("session_id")
        
        # 测试发送消息
        message_data = {
            "message": "你好，这是一个测试消息",
            "session_id": session_id
        }
        
        chat_response = requests.post(
            "http://localhost:8000/api/chat", 
            json=message_data,
            timeout=10
        )
        
        if chat_response.status_code == 200:
            response_data = chat_response.json()
            print("✅ 聊天API正常")
            print(f"   消息发送成功，响应长度: {len(response_data.get('response', ''))}")
            return True
        else:
            print(f"❌ 聊天API异常: HTTP {chat_response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 聊天API测试失败: {e}")
        return False

def test_fullscreen_layout():
    """测试全屏布局特性"""
    print("\n🎨 全屏布局特性检查:")
    
    # 这里我们通过检查前端代码来验证全屏布局实现
    try:
        with open("../frontend/src/components/SimpleChatView.vue", "r", encoding="utf-8") as f:
            content = f.read()
        
        layout_checks = [
            ("position: fixed", "固定定位"),
            ("width: 100vw", "全屏宽度"),
            ("height: 100vh", "全屏高度"),
            ("top: 0", "顶部对齐"),
            ("left: 0", "左侧对齐"),
            ("overflow: hidden", "隐藏溢出"),
            ("matechat-app", "主应用容器"),
            ("app-header", "顶部导航栏"),
            ("sidebar", "左侧边栏"),
            ("main-content", "主内容区")
        ]
        
        for keyword, description in layout_checks:
            if keyword in content:
                print(f"   ✅ {description}: 已实现")
            else:
                print(f"   ⚠️  {description}: 未找到")
        
        return True
    except Exception as e:
        print(f"❌ 布局检查失败: {e}")
        return False

def test_official_components():
    """测试MateChat官方组件使用"""
    print("\n🧩 MateChat官方组件使用检查:")
    
    try:
        with open("../frontend/src/components/SimpleChatView.vue", "r", encoding="utf-8") as f:
            content = f.read()
        
        component_checks = [
            ("McBubble", "消息气泡组件"),
            ("McIntroduction", "介绍组件"),
            ("猜你想问", "快速问题功能"),
            ("帮我写一篇文章", "示例问题1"),
            ("你可以帮我做些什么", "示例问题2"),
            ("帮我写一个快速排序", "示例问题3"),
            ("使用 js 格式化时间", "示例问题4")
        ]
        
        for keyword, description in component_checks:
            if keyword in content:
                print(f"   ✅ {description}: 已实现")
            else:
                print(f"   ⚠️  {description}: 未找到")
        
        return True
    except Exception as e:
        print(f"❌ 组件检查失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始全屏界面测试")
    print("=" * 50)
    
    # 测试项目列表
    tests = [
        ("后端健康检查", test_backend_health),
        ("前端可访问性", test_frontend_accessibility),
        ("会话管理API", test_session_api),
        ("聊天功能API", test_chat_api),
        ("全屏布局特性", test_fullscreen_layout),
        ("官方组件使用", test_official_components)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试执行失败: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结")
    print("=" * 50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！全屏界面实现成功！")
        print("\n🌟 新界面特点:")
        print("   • 完全占满浏览器窗口的全屏布局")
        print("   • 仿照MateChat官方演示页面的设计")
        print("   • 顶部导航栏 + 左侧边栏 + 右侧主内容区")
        print("   • 使用MateChat官方组件和设计规范")
        print("   • 响应式设计，自动适配浏览器大小")
        print("   • 简洁明了的用户界面")
    else:
        print("⚠️  部分测试未通过，请检查相关功能")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
