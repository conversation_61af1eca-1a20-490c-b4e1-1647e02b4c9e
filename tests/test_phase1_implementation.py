"""
第一阶段实现测试 - 核心数据层优化
测试增强数据层、智能命名系统和数据库管理功能
"""

import asyncio
import os
import tempfile
import shutil
from pathlib import Path
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_enhanced_data_layer():
    """测试增强数据层功能"""
    print("\n🧪 测试增强数据层功能...")
    
    # 创建临时数据库
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test_enhanced.db")
    
    try:
        # 导入增强数据层
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from services.enhanced_data_layer import EnhancedDataLayer
        
        # 创建数据层实例
        data_layer = EnhancedDataLayer(db_path)
        
        # 测试用户创建
        print("  📝 测试用户创建...")
        user = await data_layer.create_user(
            identifier="test_user",
            display_name="测试用户",
            metadata={"test": True}
        )
        print(f"  ✅ 用户创建成功: {user['identifier']}")
        
        # 测试会话创建
        print("  📝 测试会话创建...")
        session = await data_layer.create_session(
            user_id=user["id"],
            title=None,  # 测试无标题创建
            metadata={"test_session": True}
        )
        print(f"  ✅ 会话创建成功: {session['id']}")
        
        # 测试消息添加
        print("  📝 测试消息添加...")
        message1 = await data_layer.add_message(
            session_id=session["id"],
            role="user",
            content="你好，这是我的第一条测试消息，用来验证智能命名功能是否正常工作",
            metadata={"test_message": True}
        )
        print(f"  ✅ 消息添加成功: {message1['id']}")
        
        # 测试智能标题更新
        print("  📝 测试智能标题更新...")
        success = await data_layer.update_session_title_if_needed(
            session["id"], 
            message1["content"]
        )
        print(f"  ✅ 智能标题更新: {success}")
        
        # 验证标题是否正确生成
        updated_session = await data_layer.get_session(session["id"])
        if updated_session and updated_session.get("title"):
            print(f"  🏷️ 生成的智能标题: '{updated_session['title']}'")
        
        # 测试会话列表
        print("  📝 测试会话列表...")
        sessions = await data_layer.list_user_sessions(user["id"])
        print(f"  ✅ 用户会话数量: {len(sessions)}")
        
        # 测试消息历史
        print("  📝 测试消息历史...")
        messages = await data_layer.get_session_messages(session["id"])
        print(f"  ✅ 会话消息数量: {len(messages)}")
        
        # 测试数据库统计
        print("  📝 测试数据库统计...")
        stats = await data_layer.get_database_stats()
        print(f"  📊 数据库统计: {stats}")
        
        print("  🎉 增强数据层测试完成！")
        return True
        
    except Exception as e:
        print(f"  ❌ 增强数据层测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理临时文件
        shutil.rmtree(temp_dir, ignore_errors=True)


async def test_session_naming_service():
    """测试智能会话命名服务"""
    print("\n🧪 测试智能会话命名服务...")
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from services.session_naming_service import SessionNamingService
        
        naming_service = SessionNamingService()
        
        # 测试各种类型的消息
        test_messages = [
            "你好，我想学习Python编程",
            "请帮我翻译这段英文：Hello World",
            "写一首关于春天的诗",
            "什么是机器学习？",
            "这是一条非常长的消息，用来测试智能命名系统是否能够正确截取和处理超长文本内容，确保生成的标题不会超过预设的长度限制",
            "短消息",
            "你能帮我调试这段代码吗？",
            "今天天气怎么样？"
        ]
        
        print("  📝 测试不同类型消息的智能命名...")
        for i, message in enumerate(test_messages, 1):
            title = await naming_service.generate_intelligent_title(message)
            print(f"  {i}. 原消息: {message[:50]}{'...' if len(message) > 50 else ''}")
            print(f"     生成标题: '{title}'")
            print()
        
        # 测试候选标题生成
        print("  📝 测试候选标题生成...")
        alternatives = await naming_service.suggest_alternative_titles(
            "请帮我写一个Python函数来计算斐波那契数列", 
            count=3
        )
        print(f"  候选标题: {alternatives}")
        
        # 测试标题验证
        print("  📝 测试标题验证...")
        valid_title = "正常的标题"
        invalid_title = "这是一个超级超级超级超级超级超级长的标题"
        print(f"  '{valid_title}' 验证结果: {naming_service.validate_title(valid_title)}")
        print(f"  '{invalid_title}' 验证结果: {naming_service.validate_title(invalid_title)}")
        
        print("  🎉 智能会话命名服务测试完成！")
        return True
        
    except Exception as e:
        print(f"  ❌ 智能会话命名服务测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_database_manager():
    """测试数据库管理功能"""
    print("\n🧪 测试数据库管理功能...")
    
    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "test_manager.db")
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from services.database_manager import DatabaseConnectionPool, DatabaseHealthChecker
        
        # 测试连接池
        print("  📝 测试连接池...")
        pool = DatabaseConnectionPool(db_path, max_connections=5)
        
        # 测试连接获取
        with pool.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("CREATE TABLE test (id INTEGER PRIMARY KEY, name TEXT)")
            cursor.execute("INSERT INTO test (name) VALUES (?)", ("测试数据",))
            conn.commit()
        
        # 测试连接池统计
        stats = pool.get_stats()
        print(f"  📊 连接池统计: {stats}")
        
        # 测试健康检查
        print("  📝 测试数据库健康检查...")
        health_checker = DatabaseHealthChecker(pool)
        health_info = await health_checker.check_health()
        print(f"  🏥 健康检查结果: {health_info}")
        
        # 测试维护操作
        print("  📝 测试数据库维护...")
        maintenance_info = await health_checker.perform_maintenance()
        print(f"  🔧 维护结果: {maintenance_info}")
        
        # 清理连接池
        pool.close_all()
        
        print("  🎉 数据库管理功能测试完成！")
        return True
        
    except Exception as e:
        print(f"  ❌ 数据库管理功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


async def test_database_migration():
    """测试数据库迁移功能"""
    print("\n🧪 测试数据库迁移功能...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        
        from services.database_migration import DatabaseMigration
        
        # 创建模拟的现有数据库
        existing_db = os.path.join(temp_dir, "existing.db")
        enhanced_db = os.path.join(temp_dir, "enhanced.db")
        
        # 创建简单的现有数据库结构
        import sqlite3
        conn = sqlite3.connect(existing_db)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE checkpoints (
                thread_id TEXT,
                checkpoint_id INTEGER,
                checkpoint BLOB
            )
        """)
        cursor.execute("INSERT INTO checkpoints VALUES (?, ?, ?)", 
                      ("test_thread_1", 1, b"test_checkpoint_data"))
        cursor.execute("INSERT INTO checkpoints VALUES (?, ?, ?)", 
                      ("test_thread_2", 1, b"test_checkpoint_data"))
        conn.commit()
        conn.close()
        
        # 测试迁移
        migration = DatabaseMigration(existing_db, enhanced_db)
        
        print("  📝 测试现有数据库分析...")
        analysis = migration.analyze_existing_checkpoints()
        print(f"  📊 现有数据分析: {analysis}")
        
        print("  📝 测试数据库迁移...")
        success = migration.migrate_to_enhanced_schema()
        print(f"  🔄 迁移结果: {success}")
        
        if success:
            print("  📝 测试迁移验证...")
            verification = migration.verify_migration()
            print(f"  ✅ 验证结果: {verification}")
        
        print("  🎉 数据库迁移功能测试完成！")
        return success
        
    except Exception as e:
        print(f"  ❌ 数据库迁移功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


async def run_all_tests():
    """运行所有第一阶段测试"""
    print("🚀 开始第一阶段实现测试...")
    print("=" * 60)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(await test_enhanced_data_layer())
    test_results.append(await test_session_naming_service())
    test_results.append(await test_database_manager())
    test_results.append(await test_database_migration())
    
    # 汇总结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总:")
    
    test_names = [
        "增强数据层",
        "智能会话命名服务", 
        "数据库管理功能",
        "数据库迁移功能"
    ]
    
    passed = 0
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {i+1}. {name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{len(test_results)} 项测试通过")
    
    if passed == len(test_results):
        print("🎉 第一阶段实现测试全部通过！")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步检查和修复。")
        return False


if __name__ == "__main__":
    asyncio.run(run_all_tests())
