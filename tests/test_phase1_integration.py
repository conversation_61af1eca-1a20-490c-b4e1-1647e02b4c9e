"""
第一阶段集成测试 - 验证所有组件协同工作
测试完整的数据流：用户创建 -> 会话创建 -> 消息添加 -> 智能命名 -> 数据持久化
"""

import asyncio
import os
import tempfile
import shutil
import sys
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from services.enhanced_data_layer import EnhancedDataLayer
from services.simple_migration import simple_migrate_to_enhanced_schema

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_complete_workflow():
    """测试完整的工作流程"""
    print("\n🔄 测试完整工作流程...")

    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "integration_test.db")

    try:
        # 1. 初始化数据层
        print("  📝 步骤1: 初始化增强数据层...")
        data_layer = EnhancedDataLayer(db_path)

        # 2. 创建用户
        print("  📝 步骤2: 创建测试用户...")
        user = await data_layer.create_user(
            identifier="integration_test_user",
            display_name="集成测试用户",
            metadata={"test_type": "integration"}
        )
        print(f"    ✅ 用户创建: {user['display_name']} ({user['id']})")

        # 3. 创建多个会话并测试智能命名
        test_scenarios = [
            {
                "first_message": "你好，我想学习Python编程，请推荐一些入门资源",
                "expected_type": "learning"
            },
            {
                "first_message": "请帮我翻译这段英文：The quick brown fox jumps over the lazy dog",
                "expected_type": "translation"
            },
            {
                "first_message": "写一个计算斐波那契数列的递归函数",
                "expected_type": "programming"
            },
            {
                "first_message": "今天天气怎么样？",
                "expected_type": "general"
            }
        ]

        sessions = []
        for i, scenario in enumerate(test_scenarios, 1):
            print(f"  📝 步骤3.{i}: 创建会话并测试智能命名...")

            # 创建会话
            session = await data_layer.create_session(
                user_id=user["id"],
                metadata={"scenario": scenario["expected_type"]}
            )

            # 添加第一条消息
            await data_layer.add_message(
                session_id=session["id"],
                role="user",
                content=scenario["first_message"]
            )

            # 触发智能命名
            success = await data_layer.update_session_title_if_needed(
                session["id"], scenario["first_message"]
            )

            # 获取更新后的会话
            updated_session = await data_layer.get_session(session["id"])

            print(f"    📨 原消息: {scenario['first_message'][:50]}...")
            if updated_session:
                print(f"    🏷️ 生成标题: '{updated_session.get('title', '无标题')}'")
            print(f"    ✅ 命名成功: {success}")

            sessions.append(updated_session)

        # 4. 测试会话列表和消息历史
        print("  📝 步骤4: 测试会话管理功能...")
        user_sessions = await data_layer.list_user_sessions(user["id"])
        print(f"    📋 用户会话总数: {len(user_sessions)}")

        for session in user_sessions:
            messages = await data_layer.get_session_messages(session["id"])
            print(f"    💬 会话 '{session['title']}' 消息数: {len(messages)}")

        # 5. 测试数据库统计
        print("  📝 步骤5: 验证数据库统计...")
        stats = await data_layer.get_database_stats()
        print(f"    📊 数据库统计: {stats}")

        # 验证预期结果
        assert stats['total_users'] == 1, f"预期用户数1，实际{stats['total_users']}"
        assert stats['active_sessions'] == 4, f"预期会话数4，实际{stats['active_sessions']}"
        assert stats['total_messages'] == 4, f"预期消息数4，实际{stats['total_messages']}"

        # 6. 测试会话删除
        print("  📝 步骤6: 测试会话删除功能...")
        first_session = sessions[0]
        delete_success = await data_layer.delete_session(first_session["id"])
        print(f"    🗑️ 删除会话成功: {delete_success}")

        # 验证删除后的统计
        updated_stats = await data_layer.get_database_stats()
        assert updated_stats['active_sessions'] == 3, "删除后会话数应为3"
        assert updated_stats['total_messages'] == 3, "删除后消息数应为3"

        print("  🎉 完整工作流程测试成功！")
        return True

    except Exception as e:
        print(f"  ❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


async def test_migration_integration():
    """测试迁移集成功能"""
    print("\n🔄 测试迁移集成功能...")

    temp_dir = tempfile.mkdtemp()

    try:
        existing_db = os.path.join(temp_dir, "legacy.db")
        enhanced_db = os.path.join(temp_dir, "enhanced.db")

        # 1. 创建模拟的旧数据库
        print("  📝 步骤1: 创建模拟旧数据库...")
        import sqlite3
        conn = sqlite3.connect(existing_db)
        cursor = conn.cursor()
        cursor.execute("""
            CREATE TABLE checkpoints (
                thread_id TEXT,
                checkpoint_id INTEGER,
                checkpoint BLOB
            )
        """)

        # 插入测试数据
        test_threads = [
            ("thread_001", 1, b"checkpoint_data_1"),
            ("thread_002", 1, b"checkpoint_data_2"),
            ("thread_003", 1, b"checkpoint_data_3"),
        ]

        for thread_data in test_threads:
            cursor.execute("INSERT INTO checkpoints VALUES (?, ?, ?)", thread_data)

        conn.commit()
        conn.close()
        print(f"    ✅ 创建了 {len(test_threads)} 个测试线程")

        # 2. 执行迁移
        print("  📝 步骤2: 执行数据库迁移...")
        migration_success = simple_migrate_to_enhanced_schema(existing_db, enhanced_db)
        print(f"    🔄 迁移结果: {migration_success}")

        if not migration_success:
            return False

        # 3. 验证迁移结果
        print("  📝 步骤3: 验证迁移结果...")
        data_layer = EnhancedDataLayer(enhanced_db)

        # 检查用户
        user = await data_layer.get_user_by_identifier("default_user")
        assert user is not None, "默认用户应该存在"
        print(f"    👤 默认用户: {user['display_name']}")

        # 检查会话
        sessions = await data_layer.list_user_sessions(user["id"])
        assert len(sessions) == len(test_threads), f"预期会话数{len(test_threads)}，实际{len(sessions)}"
        print(f"    📋 迁移的会话数: {len(sessions)}")

        # 验证会话标题
        for session in sessions:
            assert session["title"] is not None, "会话应该有标题"
            print(f"    🏷️ 会话标题: '{session['title']}'")

        # 4. 测试迁移后的功能
        print("  📝 步骤4: 测试迁移后的新功能...")

        # 添加新消息到迁移的会话
        first_session = sessions[0]
        new_message = await data_layer.add_message(
            session_id=first_session["id"],
            role="user",
            content="这是迁移后添加的新消息"
        )
        print(f"    💬 新消息添加成功: {new_message['id']}")

        # 验证消息计数更新
        updated_session = await data_layer.get_session(first_session["id"])
        if updated_session:
            assert updated_session.get("message_count", 0) >= 1, "消息计数应该更新"

        print("  🎉 迁移集成测试成功！")
        return True

    except Exception as e:
        print(f"  ❌ 迁移集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


async def test_performance_and_concurrency():
    """测试性能和并发性"""
    print("\n🔄 测试性能和并发性...")

    temp_dir = tempfile.mkdtemp()
    db_path = os.path.join(temp_dir, "performance_test.db")

    try:
        data_layer = EnhancedDataLayer(db_path)

        # 1. 创建测试用户
        user = await data_layer.create_user("perf_user", "性能测试用户")

        # 2. 并发创建多个会话
        print("  📝 测试并发会话创建...")
        import time
        start_time = time.time()

        # 创建多个并发任务
        tasks = []
        for i in range(10):
            task = data_layer.create_session(
                user_id=user["id"],
                title=f"并发会话 {i+1}",
                metadata={"batch": "performance_test"}
            )
            tasks.append(task)

        # 等待所有任务完成
        sessions = await asyncio.gather(*tasks)

        creation_time = time.time() - start_time
        print(f"    ⏱️ 创建10个会话耗时: {creation_time:.3f}秒")

        # 3. 并发添加消息
        print("  📝 测试并发消息添加...")
        start_time = time.time()

        message_tasks = []
        for i, session in enumerate(sessions):
            task = data_layer.add_message(
                session_id=session["id"],
                role="user",
                content=f"并发测试消息 {i+1}"
            )
            message_tasks.append(task)

        await asyncio.gather(*message_tasks)

        message_time = time.time() - start_time
        print(f"    ⏱️ 添加10条消息耗时: {message_time:.3f}秒")

        # 4. 验证数据一致性
        print("  📝 验证数据一致性...")
        final_stats = await data_layer.get_database_stats()

        assert final_stats['total_users'] == 1, "用户数应为1"
        assert final_stats['active_sessions'] == 10, "会话数应为10"
        assert final_stats['total_messages'] == 10, "消息数应为10"

        print(f"    ✅ 数据一致性验证通过: {final_stats}")
        print("  🎉 性能和并发测试成功！")
        return True

    except Exception as e:
        print(f"  ❌ 性能和并发测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        shutil.rmtree(temp_dir, ignore_errors=True)


async def run_integration_tests():
    """运行所有集成测试"""
    print("🚀 开始第一阶段集成测试...")
    print("=" * 70)

    test_functions = [
        ("完整工作流程", test_complete_workflow),
        ("迁移集成功能", test_migration_integration),
        ("性能和并发性", test_performance_and_concurrency)
    ]

    results = []
    for test_name, test_func in test_functions:
        print(f"\n🧪 运行测试: {test_name}")
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 {test_name} 异常: {e}")
            results.append((test_name, False))

    # 汇总结果
    print("\n" + "=" * 70)
    print("📊 集成测试结果汇总:")

    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  • {test_name}: {status}")
        if result:
            passed += 1

    print(f"\n🎯 总体结果: {passed}/{len(results)} 项集成测试通过")

    if passed == len(results):
        print("🎉 第一阶段集成测试全部通过！")
        print("✨ 核心数据层优化实现完成，可以进入第二阶段开发。")
        return True
    else:
        print("⚠️ 部分集成测试失败，需要进一步检查和修复。")
        return False


if __name__ == "__main__":
    asyncio.run(run_integration_tests())