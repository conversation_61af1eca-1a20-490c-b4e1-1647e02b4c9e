"""
会话管理API模块 - 第二阶段实现
集成增强数据层，提供完整的会话CRUD操作
借鉴LangGraphChainlitAgent的API设计模式
"""

import uuid
from typing import List, Optional, Dict, Any
from datetime import datetime

from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

# 导入第一阶段实现的增强数据层
from services.enhanced_data_layer import EnhancedDataLayer
from services.session_naming_service import SessionNamingService
from services.session_deletion_service_simple import SessionDeletionService
from services.session_recovery_service import SessionRecoveryService
from services.session_monitoring_service import SessionMonitoringService

# 创建路由器
router = APIRouter(prefix="/api/sessions", tags=["会话管理"])

# 全局服务实例
data_layer: Optional[EnhancedDataLayer] = None
naming_service: Optional[SessionNamingService] = None
deletion_service: Optional[SessionDeletionService] = None
monitoring_service: Optional[SessionMonitoringService] = None


def get_data_layer() -> EnhancedDataLayer:
    """获取数据层实例"""
    global data_layer
    if data_layer is None:
        data_layer = EnhancedDataLayer("./data/enhanced_sessions.db")
    return data_layer


def get_naming_service() -> SessionNamingService:
    """获取命名服务实例"""
    global naming_service
    if naming_service is None:
        naming_service = SessionNamingService()
    return naming_service


def get_deletion_service() -> SessionDeletionService:
    """获取删除服务实例"""
    global deletion_service
    if deletion_service is None:
        deletion_service = SessionDeletionService(get_data_layer())
    return deletion_service


# 全局恢复服务实例
recovery_service: Optional[SessionRecoveryService] = None


def get_recovery_service() -> SessionRecoveryService:
    """获取恢复服务实例"""
    global recovery_service
    if recovery_service is None:
        from core.agent_core import AgentCore
        data_layer = get_data_layer()
        agent_core = AgentCore()
        recovery_service = SessionRecoveryService(data_layer, agent_core)
    return recovery_service


def get_monitoring_service() -> SessionMonitoringService:
    """获取监控服务实例"""
    global monitoring_service
    if monitoring_service is None:
        data_layer = get_data_layer()
        monitoring_service = SessionMonitoringService(data_layer)
    return monitoring_service


# === Pydantic 模型定义 ===

class CreateSessionRequest(BaseModel):
    """创建会话请求模型"""
    title: Optional[str] = Field(None, description="会话标题，如果为空将自动生成")
    user_id: Optional[str] = Field("default_user", description="用户ID")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")


class UpdateSessionRequest(BaseModel):
    """更新会话请求模型"""
    title: Optional[str] = Field(None, description="新的会话标题")
    metadata: Optional[Dict[str, Any]] = Field(None, description="更新的元数据")


class SessionResponse(BaseModel):
    """会话响应模型"""
    id: str = Field(..., description="会话ID")
    user_id: str = Field(..., description="用户ID")
    title: str = Field(..., description="会话标题")
    created_at: datetime = Field(..., description="创建时间")
    updated_at: datetime = Field(..., description="更新时间")
    last_activity: datetime = Field(..., description="最后活动时间")
    message_count: int = Field(..., description="消息数量")
    is_active: bool = Field(..., description="是否活跃")
    metadata: Optional[Dict[str, Any]] = Field(None, description="会话元数据")


class SessionListResponse(BaseModel):
    """会话列表响应模型"""
    sessions: List[SessionResponse] = Field(..., description="会话列表")
    total: int = Field(..., description="总数量")
    page: int = Field(..., description="当前页码")
    page_size: int = Field(..., description="每页大小")


class MessageResponse(BaseModel):
    """消息响应模型"""
    id: str = Field(..., description="消息ID")
    session_id: str = Field(..., description="会话ID")
    role: str = Field(..., description="消息角色")
    content: str = Field(..., description="消息内容")
    created_at: datetime = Field(..., description="创建时间")
    metadata: Optional[Dict[str, Any]] = Field(None, description="消息元数据")


class SessionHistoryResponse(BaseModel):
    """会话历史响应模型"""
    session_id: str = Field(..., description="会话ID")
    messages: List[MessageResponse] = Field(..., description="消息列表")
    total_messages: int = Field(..., description="总消息数")


class DeleteSessionResponse(BaseModel):
    """删除会话响应模型"""
    session_id: str = Field(..., description="被删除的会话ID")
    deleted: bool = Field(..., description="是否删除成功")
    deletion_type: str = Field(..., description="删除类型: soft 或 hard")
    can_recover: bool = Field(..., description="是否可以恢复")
    message: str = Field(..., description="删除结果消息")
    deletion_info: Optional[Dict[str, Any]] = Field(None, description="删除详细信息")


class BatchDeleteRequest(BaseModel):
    """批量删除请求模型"""
    session_ids: List[str] = Field(..., description="要删除的会话ID列表")
    deletion_type: str = Field("soft", description="删除类型: soft 或 hard")
    reason: Optional[str] = Field(None, description="删除原因")


class BatchDeleteResponse(BaseModel):
    """批量删除响应模型"""
    total_requested: int = Field(..., description="请求删除的总数")
    successful_deletions: List[Dict[str, Any]] = Field(..., description="成功删除的会话")
    failed_deletions: List[Dict[str, Any]] = Field(..., description="删除失败的会话")
    deletion_type: str = Field(..., description="删除类型")


class RecoverSessionResponse(BaseModel):
    """恢复会话响应模型"""
    session_id: str = Field(..., description="恢复的会话ID")
    recovered: bool = Field(..., description="是否恢复成功")
    recovery_time: str = Field(..., description="恢复时间")
    message: str = Field(..., description="恢复结果消息")


class SessionContextRecoveryResponse(BaseModel):
    """会话上下文恢复响应模型"""
    success: bool = Field(..., description="是否恢复成功")
    session_id: str = Field(..., description="会话ID")
    message_count: Optional[int] = Field(None, description="恢复的消息数量")
    langgraph_message_count: Optional[int] = Field(None, description="LangGraph状态中的消息数量")
    recovery_time: Optional[str] = Field(None, description="恢复时间")
    session_info: Optional[Dict[str, Any]] = Field(None, description="会话信息")
    recovery_details: Optional[Dict[str, Any]] = Field(None, description="恢复详细信息")
    error: Optional[str] = Field(None, description="错误信息")


class RecoverableSessionInfo(BaseModel):
    """可恢复会话信息模型"""
    session_id: str = Field(..., description="会话ID")
    title: str = Field(..., description="会话标题")
    created_at: str = Field(..., description="创建时间")
    last_activity: str = Field(..., description="最后活动时间")
    message_count: int = Field(..., description="消息数量")
    is_active: bool = Field(..., description="是否活跃")
    can_recover: bool = Field(..., description="是否可以恢复")


class RecoverableSessionsResponse(BaseModel):
    """可恢复会话列表响应模型"""
    user_id: str = Field(..., description="用户ID")
    sessions: List[RecoverableSessionInfo] = Field(..., description="可恢复会话列表")
    total_sessions: int = Field(..., description="总会话数")


class SessionIntegrityResponse(BaseModel):
    """会话完整性检查响应模型"""
    valid: bool = Field(..., description="会话是否有效")
    session_id: Optional[str] = Field(None, description="会话ID")
    metadata_exists: Optional[bool] = Field(None, description="元数据是否存在")
    message_count: Optional[int] = Field(None, description="数据库中的消息数量")
    langgraph_message_count: Optional[int] = Field(None, description="LangGraph状态中的消息数量")
    state_consistent: Optional[bool] = Field(None, description="状态是否一致")
    session_info: Optional[Dict[str, Any]] = Field(None, description="会话信息")
    error: Optional[str] = Field(None, description="错误信息")


class SessionStatsResponse(BaseModel):
    """会话统计响应模型"""
    total_sessions: int = Field(..., description="总会话数")
    active_sessions: int = Field(..., description="活跃会话数")
    total_messages: int = Field(..., description="总消息数")
    total_users: int = Field(..., description="总用户数")


# === API 端点实现 ===

@router.post("/", response_model=SessionResponse, summary="创建新会话")
async def create_session(
    request: CreateSessionRequest,
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    创建新会话
    
    - 如果提供了title，直接使用
    - 如果没有提供title，将在第一条消息时自动生成智能标题
    - 自动创建用户（如果不存在）
    """
    try:
        # 确保用户存在
        user = await data_layer.get_user_by_identifier(request.user_id)
        if not user:
            user = await data_layer.create_user(
                identifier=request.user_id,
                display_name=f"用户 {request.user_id}",
                metadata={"auto_created": True}
            )
        
        # 创建会话
        session = await data_layer.create_session(
            user_id=user["id"],
            title=request.title,
            metadata=request.metadata or {}
        )
        
        return SessionResponse(
            id=session["id"],
            user_id=user["identifier"],
            title=session["title"] or "新对话",
            created_at=datetime.fromisoformat(session["created_at"]),
            updated_at=datetime.fromisoformat(session["updated_at"]),
            last_activity=datetime.fromisoformat(session["last_activity"]),
            message_count=session["message_count"],
            is_active=session["is_active"],
            metadata=session.get("metadata")
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"创建会话失败: {str(e)}")


@router.get("/", response_model=SessionListResponse, summary="获取会话列表")
async def list_sessions(
    user_id: str = Query("default_user", description="用户ID"),
    page: int = Query(1, ge=1, description="页码"),
    page_size: int = Query(20, ge=1, le=100, description="每页大小"),
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    获取用户的会话列表
    
    - 支持分页
    - 按最后活动时间倒序排列
    - 包含会话基本信息和消息统计
    """
    try:
        # 获取用户
        user = await data_layer.get_user_by_identifier(user_id)
        if not user:
            raise HTTPException(status_code=404, detail="用户不存在")
        
        # 获取会话列表（这里简化实现，实际应该支持分页）
        sessions = await data_layer.list_user_sessions(user["id"], limit=page_size)
        
        session_responses = []
        for session in sessions:
            session_responses.append(SessionResponse(
                id=session["id"],
                user_id=user_id,
                title=session["title"] or "无标题",
                created_at=datetime.fromisoformat(session["created_at"]),
                updated_at=datetime.fromisoformat(session["updated_at"]),
                last_activity=datetime.fromisoformat(session["last_activity"]),
                message_count=session["message_count"],
                is_active=session["is_active"],
                metadata=session.get("metadata")
            ))
        
        return SessionListResponse(
            sessions=session_responses,
            total=len(session_responses),
            page=page,
            page_size=page_size
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话列表失败: {str(e)}")


@router.get("/deleted", summary="获取已删除会话列表")
async def get_deleted_sessions(
    user_id: Optional[str] = Query(None, description="用户ID过滤"),
    limit: int = Query(50, description="返回数量限制", ge=1, le=100),
    deletion_service: SessionDeletionService = Depends(get_deletion_service)
):
    """
    获取已删除的会话列表

    - 只返回软删除的会话
    - 支持按用户ID过滤
    - 按删除时间倒序排列
    """
    try:
        deleted_sessions = await deletion_service.get_deleted_sessions(
            user_id=user_id,
            limit=limit
        )

        return {
            "deleted_sessions": deleted_sessions,
            "total_count": len(deleted_sessions),
            "user_filter": user_id,
            "limit": limit
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取已删除会话失败: {str(e)}")


@router.get("/{session_id}", response_model=SessionResponse, summary="获取会话详情")
async def get_session(
    session_id: str,
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    获取指定会话的详细信息
    """
    try:
        session = await data_layer.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 获取用户信息
        user = await data_layer.get_user_by_id(session["user_id"])
        user_identifier = user["identifier"] if user else "unknown"
        
        return SessionResponse(
            id=session["id"],
            user_id=user_identifier,
            title=session["title"] or "无标题",
            created_at=datetime.fromisoformat(session["created_at"]),
            updated_at=datetime.fromisoformat(session["updated_at"]),
            last_activity=datetime.fromisoformat(session["last_activity"]),
            message_count=session["message_count"],
            is_active=session["is_active"],
            metadata=session.get("metadata")
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话详情失败: {str(e)}")


@router.put("/{session_id}", response_model=SessionResponse, summary="更新会话")
async def update_session(
    session_id: str,
    request: UpdateSessionRequest,
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    更新会话信息
    
    - 可以更新标题和元数据
    - 自动更新最后修改时间
    """
    try:
        # 检查会话是否存在
        session = await data_layer.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 更新会话
        success = await data_layer.update_session(
            session_id=session_id,
            title=request.title,
            metadata=request.metadata
        )
        
        if not success:
            raise HTTPException(status_code=500, detail="更新会话失败")
        
        # 返回更新后的会话信息
        return await get_session(session_id, data_layer)
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新会话失败: {str(e)}")


@router.get("/{session_id}/messages", response_model=SessionHistoryResponse, summary="获取会话消息历史")
async def get_session_messages(
    session_id: str,
    limit: int = Query(100, ge=1, le=1000, description="消息数量限制"),
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    获取会话的消息历史
    
    - 按时间顺序返回消息
    - 支持限制返回数量
    """
    try:
        # 检查会话是否存在
        session = await data_layer.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")
        
        # 获取消息列表
        messages = await data_layer.get_session_messages(session_id, limit=limit)
        
        message_responses = []
        for message in messages:
            message_responses.append(MessageResponse(
                id=message["id"],
                session_id=message["session_id"],
                role=message["role"],
                content=message["content"],
                created_at=datetime.fromisoformat(message["created_at"]),
                metadata=message.get("metadata")
            ))
        
        return SessionHistoryResponse(
            session_id=session_id,
            messages=message_responses,
            total_messages=len(message_responses)
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取会话消息失败: {str(e)}")


@router.delete("/{session_id}", response_model=DeleteSessionResponse, summary="软删除会话")
async def soft_delete_session(
    session_id: str,
    reason: Optional[str] = Query(None, description="删除原因"),
    deletion_service: SessionDeletionService = Depends(get_deletion_service)
):
    """
    软删除指定会话

    - 标记会话为已删除状态，但保留数据
    - 可以通过恢复接口恢复会话
    - 包含删除原因和时间戳
    """
    try:
        result = await deletion_service.soft_delete_session(
            session_id=session_id,
            reason=reason
        )

        return DeleteSessionResponse(
            session_id=session_id,
            deleted=result["deleted"],
            deletion_type=result["deletion_type"],
            can_recover=result["can_recover"],
            message="会话已软删除，可通过恢复接口恢复",
            deletion_info=result["deletion_info"]
        )

    except Exception as e:
        if "不存在" in str(e):
            raise HTTPException(status_code=404, detail=str(e))
        elif "已被删除" in str(e):
            raise HTTPException(status_code=400, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"软删除会话失败: {str(e)}")


@router.delete("/{session_id}/hard", response_model=DeleteSessionResponse, summary="硬删除会话")
async def hard_delete_session(
    session_id: str,
    confirm: bool = Query(False, description="确认删除标志，必须为true"),
    deletion_service: SessionDeletionService = Depends(get_deletion_service)
):
    """
    硬删除指定会话

    - 完全删除会话及其所有相关数据
    - 包括消息历史、检查点和元数据
    - 不可恢复操作，请谨慎使用
    - 需要明确确认才能执行
    """
    if not confirm:
        raise HTTPException(
            status_code=400,
            detail="硬删除需要明确确认，请设置 confirm=true"
        )

    try:
        result = await deletion_service.hard_delete_session(
            session_id=session_id,
            confirm_deletion=True
        )

        return DeleteSessionResponse(
            session_id=session_id,
            deleted=result["deleted"],
            deletion_type=result["deletion_type"],
            can_recover=result["can_recover"],
            message="会话已永久删除，无法恢复",
            deletion_info=result.get("deleted_data")
        )

    except Exception as e:
        if "不存在" in str(e):
            raise HTTPException(status_code=404, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"硬删除会话失败: {str(e)}")


@router.post("/batch-delete", response_model=BatchDeleteResponse, summary="批量删除会话")
async def batch_delete_sessions(
    request: BatchDeleteRequest,
    deletion_service: SessionDeletionService = Depends(get_deletion_service)
):
    """
    批量删除会话

    - 支持软删除和硬删除
    - 返回成功和失败的详细信息
    - 适用于清理大量会话
    """
    try:
        result = await deletion_service.batch_delete_sessions(
            session_ids=request.session_ids,
            deletion_type=request.deletion_type,
            reason=request.reason
        )

        return BatchDeleteResponse(**result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量删除失败: {str(e)}")


@router.post("/{session_id}/recover", response_model=RecoverSessionResponse, summary="恢复已删除会话")
async def recover_deleted_session(
    session_id: str,
    deletion_service: SessionDeletionService = Depends(get_deletion_service)
):
    """
    恢复软删除的会话

    - 只能恢复软删除的会话
    - 硬删除的会话无法恢复
    - 恢复后会话状态回到正常
    """
    try:
        result = await deletion_service.recover_session(session_id)

        return RecoverSessionResponse(
            session_id=session_id,
            recovered=result["recovered"],
            recovery_time=result["recovery_time"],
            message="会话已成功恢复"
        )

    except Exception as e:
        if "不存在" in str(e):
            raise HTTPException(status_code=404, detail=str(e))
        elif "未被删除" in str(e):
            raise HTTPException(status_code=400, detail=str(e))
        else:
            raise HTTPException(status_code=500, detail=f"恢复会话失败: {str(e)}")



@router.post("/{session_id}/messages", summary="添加消息到会话")
async def add_message_to_session(
    session_id: str,
    role: str,
    content: str,
    metadata: Optional[Dict[str, Any]] = None,
    data_layer: EnhancedDataLayer = Depends(get_data_layer),
    naming_service: SessionNamingService = Depends(get_naming_service)
):
    """
    向会话添加新消息

    - 支持用户和助手消息
    - 自动更新会话的最后活动时间
    - 如果是第一条用户消息，自动生成智能标题
    """
    try:
        # 检查会话是否存在
        session = await data_layer.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        # 添加消息
        message = await data_layer.add_message(
            session_id=session_id,
            role=role,
            content=content,
            metadata=metadata or {}
        )

        # 如果是用户的第一条消息且会话没有标题，生成智能标题
        if role == "user" and (not session["title"] or session["title"] == "新对话"):
            intelligent_title = await naming_service.generate_intelligent_title(content)
            await data_layer.update_session_title_if_needed(session_id, intelligent_title)

        return MessageResponse(
            id=message["id"],
            session_id=message["session_id"],
            role=message["role"],
            content=message["content"],
            created_at=datetime.fromisoformat(message["created_at"]),
            metadata=message.get("metadata")
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"添加消息失败: {str(e)}")


@router.get("/stats/overview", response_model=SessionStatsResponse, summary="获取会话统计")
async def get_session_stats(
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    获取整体会话统计信息
    """
    try:
        stats = await data_layer.get_database_stats()

        return SessionStatsResponse(
            total_sessions=stats["active_sessions"] + stats["inactive_sessions"],
            active_sessions=stats["active_sessions"],
            total_messages=stats["total_messages"],
            total_users=stats["total_users"]
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")


@router.post("/{session_id}/activate", summary="激活会话")
async def activate_session(
    session_id: str,
    data_layer: EnhancedDataLayer = Depends(get_data_layer)
):
    """
    激活指定会话

    - 将会话标记为活跃状态
    - 更新最后活动时间
    """
    try:
        session = await data_layer.get_session(session_id)
        if not session:
            raise HTTPException(status_code=404, detail="会话不存在")

        success = await data_layer.update_session(
            session_id=session_id,
            metadata={**session.get("metadata", {}), "is_active": True}
        )

        if not success:
            raise HTTPException(status_code=500, detail="激活会话失败")

        return {"session_id": session_id, "activated": True, "message": "会话已激活"}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"激活会话失败: {str(e)}")


# === 会话恢复相关API ===

@router.post("/{session_id}/restore", response_model=SessionContextRecoveryResponse, summary="恢复会话上下文")
async def restore_session_context(
    session_id: str,
    recovery_service: SessionRecoveryService = Depends(get_recovery_service)
):
    """
    恢复会话的LangGraph上下文

    - 从数据库加载历史消息
    - 重建LangGraph状态
    - 恢复工具调用上下文
    - 确保状态机连续性
    """
    try:
        result = await recovery_service.restore_session_context(session_id)

        return SessionContextRecoveryResponse(
            success=result["success"],
            session_id=result["session_id"],
            message_count=result.get("message_count"),
            langgraph_message_count=result.get("langgraph_message_count"),
            recovery_time=result.get("recovery_time"),
            session_info=result.get("session_info"),
            recovery_details=result.get("recovery_details"),
            error=result.get("error")
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"恢复会话上下文失败: {str(e)}")


@router.get("/recoverable/{user_id}", response_model=RecoverableSessionsResponse, summary="获取可恢复会话列表")
async def get_recoverable_sessions(
    user_id: str,
    limit: int = Query(20, ge=1, le=100, description="返回数量限制"),
    recovery_service: SessionRecoveryService = Depends(get_recovery_service)
):
    """
    获取用户的可恢复会话列表

    - 按最后活动时间排序
    - 只返回有消息的活跃会话
    - 排除已删除的会话
    """
    try:
        sessions = await recovery_service.get_recoverable_sessions(user_id, limit)

        session_infos = []
        for session in sessions:
            session_infos.append(RecoverableSessionInfo(
                session_id=session["session_id"],
                title=session["title"],
                created_at=session["created_at"],
                last_activity=session["last_activity"],
                message_count=session["message_count"],
                is_active=session["is_active"],
                can_recover=session["can_recover"]
            ))

        return RecoverableSessionsResponse(
            user_id=user_id,
            sessions=session_infos,
            total_sessions=len(session_infos)
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取可恢复会话列表失败: {str(e)}")


@router.get("/{session_id}/integrity", response_model=SessionIntegrityResponse, summary="检查会话完整性")
async def check_session_integrity(
    session_id: str,
    recovery_service: SessionRecoveryService = Depends(get_recovery_service)
):
    """
    检查会话的数据完整性

    - 验证会话元数据
    - 检查消息数据
    - 验证LangGraph状态一致性
    """
    try:
        result = await recovery_service.validate_session_integrity(session_id)

        return SessionIntegrityResponse(
            valid=result["valid"],
            session_id=result.get("session_id"),
            metadata_exists=result.get("metadata_exists"),
            message_count=result.get("message_count"),
            langgraph_message_count=result.get("langgraph_message_count"),
            state_consistent=result.get("state_consistent"),
            session_info=result.get("session_info"),
            error=result.get("error")
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"检查会话完整性失败: {str(e)}")


# === 辅助函数 ===

# === 监控API端点 ===

@router.get("/monitoring/active-sessions", summary="获取活跃会话统计")
async def get_active_sessions_stats(
    time_window_hours: int = Query(24, description="时间窗口（小时）"),
    monitoring_service: SessionMonitoringService = Depends(get_monitoring_service)
):
    """获取活跃会话数量统计"""
    try:
        stats = await monitoring_service.get_active_sessions_count(time_window_hours)
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取活跃会话统计失败: {e}")


@router.get("/monitoring/session-duration", summary="获取会话持续时间分析")
async def get_session_duration_analysis(
    limit: int = Query(100, description="分析的会话数量限制"),
    monitoring_service: SessionMonitoringService = Depends(get_monitoring_service)
):
    """分析会话持续时间"""
    try:
        analysis = await monitoring_service.get_session_duration_analysis(limit)
        return {
            "success": True,
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"会话持续时间分析失败: {e}")


@router.get("/monitoring/message-frequency", summary="获取消息频率统计")
async def get_message_frequency_stats(
    days: int = Query(7, description="统计天数"),
    monitoring_service: SessionMonitoringService = Depends(get_monitoring_service)
):
    """获取消息频率统计"""
    try:
        stats = await monitoring_service.get_message_frequency_stats(days)
        return {
            "success": True,
            "data": stats
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"消息频率统计失败: {e}")


@router.get("/monitoring/user-activity", summary="获取用户活跃度分析")
async def get_user_activity_analysis(
    limit: int = Query(50, description="分析的用户数量限制"),
    monitoring_service: SessionMonitoringService = Depends(get_monitoring_service)
):
    """分析用户活跃度"""
    try:
        analysis = await monitoring_service.get_user_activity_analysis(limit)
        return {
            "success": True,
            "data": analysis
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"用户活跃度分析失败: {e}")


@router.get("/monitoring/system-health", summary="获取系统健康指标")
async def get_system_health_metrics(
    monitoring_service: SessionMonitoringService = Depends(get_monitoring_service)
):
    """获取系统健康指标"""
    try:
        metrics = await monitoring_service.get_system_health_metrics()
        return {
            "success": True,
            "data": metrics
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"系统健康指标获取失败: {e}")


async def initialize_session_api():
    """初始化会话API模块"""
    global data_layer, naming_service

    if data_layer is None:
        data_layer = EnhancedDataLayer("./data/enhanced_sessions.db")
        # EnhancedDataLayer 在 __init__ 中完成初始化，无需额外调用

    if naming_service is None:
        naming_service = SessionNamingService()

    print("✅ 会话管理API模块已初始化")
