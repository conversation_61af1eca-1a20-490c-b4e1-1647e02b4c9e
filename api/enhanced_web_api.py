"""
增强的Web API - 集成第二阶段会话管理功能
基于现有的web_api.py，集成新的会话管理API
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 导入核心组件
from core.agent_core import agent_core
from services.config_manager import config_manager
from services.error_handler import error_handler, CustomError, ErrorCode

# 导入第二阶段的会话管理API
from api.session_management import router as session_router, initialize_session_api
from api.session_management import get_data_layer, get_naming_service

# 导入现有的模型定义
from interfaces.web_api import (
    ChatMessage, ChatRequest, ChatResponse, HealthResponse, WebSocketManager
)


# === 增强的聊天请求模型 ===

class EnhancedChatRequest(BaseModel):
    """增强的聊天请求模型，集成会话管理"""
    message: str = Field(..., description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空将创建新会话")
    user_id: str = Field("default_user", description="用户ID")
    stream: bool = Field(False, description="是否流式响应")
    auto_title: bool = Field(True, description="是否自动生成会话标题")


class EnhancedChatResponse(BaseModel):
    """增强的聊天响应模型"""
    message: str = Field(..., description="AI回复")
    session_id: str = Field(..., description="会话ID")
    session_title: Optional[str] = Field(None, description="会话标题")
    user_id: str = Field(..., description="用户ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    is_new_session: bool = Field(False, description="是否为新创建的会话")


# === 创建增强的FastAPI应用 ===

def create_enhanced_app() -> FastAPI:
    """创建增强的FastAPI应用实例"""
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    app = FastAPI(
        title="LangGraph Agent Enhanced Web API",
        description="基于LangGraph的智能助手增强Web API接口 - 集成完整会话管理",
        version="2.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=web_config.get('cors_origins', ['*']),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 创建WebSocket管理器
    websocket_manager = WebSocketManager()
    
    # === 启动事件 ===
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动时的初始化"""
        print("🚀 启动增强Web API服务...")
        
        # 初始化会话管理API
        await initialize_session_api()
        
        # 初始化agent核心
        await agent_core.initialize()
        
        print("✅ 增强Web API服务启动完成")
    
    # === 基础端点 ===
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        return HealthResponse(
            status="healthy",
            version="2.1.0"
        )
    
    @app.get("/")
    async def root():
        """根端点"""
        return {
            "message": "LangGraph Agent Enhanced Web API",
            "version": "2.1.0",
            "features": [
                "完整会话管理",
                "智能会话命名",
                "用户会话隔离",
                "消息历史管理",
                "实时WebSocket通信"
            ]
        }
    
    # === 增强的聊天端点 ===
    
    @app.post("/api/chat", response_model=EnhancedChatResponse)
    async def enhanced_chat_endpoint(
        request: EnhancedChatRequest,
        data_layer = Depends(get_data_layer),
        naming_service = Depends(get_naming_service)
    ):
        """
        增强的聊天API端点
        
        - 集成完整的会话管理
        - 自动创建用户和会话
        - 智能会话命名
        - 消息历史保存
        """
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            is_new_session = False
            session_id = request.session_id
            session_title = None
            
            # 如果没有提供session_id，创建新会话
            if not session_id:
                # 确保用户存在
                user = await data_layer.get_user_by_identifier(request.user_id)
                if not user:
                    user = await data_layer.create_user(
                        identifier=request.user_id,
                        display_name=f"用户 {request.user_id}",
                        metadata={"auto_created": True}
                    )
                
                # 创建新会话
                session = await data_layer.create_session(
                    user_id=user["id"],
                    title=None,  # 将在第一条消息后自动生成
                    metadata={"created_via": "chat_api"}
                )
                session_id = session["id"]
                is_new_session = True
                print(f"📝 创建新会话: {session_id}")
            
            # 保存用户消息到数据库
            await data_layer.add_message(
                session_id=session_id,
                role="user",
                content=request.message,
                metadata={"source": "chat_api"}
            )
            
            # 处理消息（调用LangGraph Agent）
            response = await agent_core.process_message(
                message=request.message,
                thread_id=session_id  # 使用session_id作为thread_id
            )
            
            # 保存AI回复到数据库
            await data_layer.add_message(
                session_id=session_id,
                role="assistant",
                content=response["response"],
                metadata={"source": "agent_core"}
            )
            
            # 如果是新会话且启用自动标题，生成智能标题
            if is_new_session and request.auto_title:
                session_title = await naming_service.generate_intelligent_title(request.message)
                await data_layer.update_session_title_if_needed(session_id, session_title)
                print(f"🏷️ 生成会话标题: {session_title}")
            else:
                # 获取现有会话标题
                session = await data_layer.get_session(session_id)
                session_title = session["title"] if session else None
            
            return EnhancedChatResponse(
                message=response["response"],
                session_id=session_id,
                session_title=session_title,
                user_id=request.user_id,
                is_new_session=is_new_session
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {
                "endpoint": "enhanced_chat", 
                "request": request.model_dump()
            })
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === 流式聊天端点 ===
    
    @app.post("/api/chat/stream")
    async def enhanced_stream_chat_endpoint(
        request: EnhancedChatRequest,
        data_layer = Depends(get_data_layer)
    ):
        """
        增强的流式聊天端点
        
        - 支持Server-Sent Events (SSE)
        - 实时返回AI生成的内容
        - 集成会话管理
        """
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 会话管理逻辑（与普通聊天端点相同）
            is_new_session = False
            session_id = request.session_id
            
            if not session_id:
                user = await data_layer.get_user_by_identifier(request.user_id)
                if not user:
                    user = await data_layer.create_user(
                        identifier=request.user_id,
                        display_name=f"用户 {request.user_id}",
                        metadata={"auto_created": True}
                    )
                
                session = await data_layer.create_session(
                    user_id=user["id"],
                    title=None,
                    metadata={"created_via": "stream_chat_api"}
                )
                session_id = session["id"]
                is_new_session = True
            
            # 保存用户消息
            await data_layer.add_message(
                session_id=session_id,
                role="user",
                content=request.message,
                metadata={"source": "stream_chat_api"}
            )
            
            # 流式处理（这里简化实现，实际需要支持真正的流式响应）
            response = await agent_core.process_message(
                message=request.message,
                thread_id=session_id
            )
            
            # 保存AI回复
            await data_layer.add_message(
                session_id=session_id,
                role="assistant",
                content=response["response"],
                metadata={"source": "agent_core_stream"}
            )
            
            return {
                "session_id": session_id,
                "response": response["response"],
                "is_new_session": is_new_session,
                "stream": True
            }
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {
                "endpoint": "enhanced_stream_chat", 
                "request": request.model_dump()
            })
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === 包含会话管理路由 ===
    
    app.include_router(session_router, tags=["会话管理"])
    
    # === 工具相关端点 ===
    
    @app.get("/api/tools")
    async def get_available_tools():
        """获取可用工具列表"""
        try:
            await agent_core.initialize()
            tools = agent_core.get_available_tools()
            return {"tools": tools}
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "tools"})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === WebSocket端点 ===
    
    @app.websocket("/ws/{session_id}")
    async def websocket_endpoint(websocket: WebSocket, session_id: str):
        """WebSocket聊天端点"""
        connection_id = f"ws_{uuid.uuid4().hex[:8]}"
        
        try:
            await websocket_manager.connect(websocket, connection_id, session_id)
            
            while True:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)
                
                # 处理消息（这里可以集成会话管理）
                user_message = message_data.get("message", "")
                user_id = message_data.get("user_id", "default_user")
                
                if user_message:
                    # 使用增强的聊天逻辑
                    enhanced_request = EnhancedChatRequest(
                        message=user_message,
                        session_id=session_id,
                        user_id=user_id,
                        stream=False
                    )
                    
                    # 这里可以调用enhanced_chat_endpoint的逻辑
                    # 简化实现，直接返回响应
                    await websocket.send_text(json.dumps({
                        "type": "message",
                        "session_id": session_id,
                        "message": f"收到消息: {user_message}",
                        "timestamp": datetime.now().isoformat()
                    }))
                
        except WebSocketDisconnect:
            websocket_manager.disconnect(connection_id)
            print(f"WebSocket连接断开: {connection_id}")
        except Exception as e:
            print(f"WebSocket错误: {e}")
            await websocket.close()
    
    return app


# === 应用实例 ===

# 创建应用实例
enhanced_app = create_enhanced_app()

# 兼容性别名
app = enhanced_app
