"""
增强的Web API - 集成第二阶段会话管理功能
基于现有的web_api.py，集成新的会话管理API
"""

import asyncio
import json
import uuid
from typing import Dict, List, Optional, Any
from datetime import datetime

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

# 导入核心组件
from core.agent_core import agent_core
from services.config_manager import config_manager
from services.error_handler import error_handler, CustomError, ErrorCode

# 导入第二阶段的会话管理API
from api.session_management import router as session_router, initialize_session_api
from api.session_management import get_data_layer, get_naming_service, get_deletion_service

# === 基础数据模型定义 ===

class ChatMessage(BaseModel):
    """聊天消息模型"""
    role: str = Field(..., description="消息角色: user, assistant, system")
    content: str = Field(..., description="消息内容")
    timestamp: Optional[datetime] = Field(default_factory=datetime.now, description="时间戳")


class ChatRequest(BaseModel):
    """聊天请求模型"""
    message: str = Field(..., description="用户消息")
    thread_id: Optional[str] = Field(None, description="会话ID")
    stream: bool = Field(False, description="是否流式响应")


class ChatResponse(BaseModel):
    """聊天响应模型"""
    message: str = Field(..., description="AI回复")
    thread_id: str = Field(..., description="会话ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")


class HealthResponse(BaseModel):
    """健康检查响应模型"""
    status: str = Field(..., description="服务状态")
    timestamp: datetime = Field(default_factory=datetime.now, description="检查时间")
    version: str = Field(..., description="版本信息")


class WebSocketManager:
    """WebSocket连接管理器"""

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        self.connection_threads: Dict[str, str] = {}  # connection_id -> thread_id

    async def connect(self, websocket: WebSocket, connection_id: str, thread_id: Optional[str] = None):
        """接受WebSocket连接"""
        await websocket.accept()
        self.active_connections[connection_id] = websocket
        if thread_id:
            self.connection_threads[connection_id] = thread_id
        print(f"WebSocket连接已建立: {connection_id}")

    def disconnect(self, connection_id: str):
        """断开WebSocket连接"""
        if connection_id in self.active_connections:
            del self.active_connections[connection_id]
        if connection_id in self.connection_threads:
            del self.connection_threads[connection_id]
        print(f"WebSocket连接已断开: {connection_id}")

    async def send_message(self, connection_id: str, message: dict):
        """发送消息到指定连接"""
        if connection_id in self.active_connections:
            websocket = self.active_connections[connection_id]
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"发送WebSocket消息失败: {e}")
                self.disconnect(connection_id)

    async def broadcast(self, message: dict):
        """广播消息到所有连接"""
        disconnected = []
        for connection_id, websocket in self.active_connections.items():
            try:
                await websocket.send_text(json.dumps(message, ensure_ascii=False))
            except Exception as e:
                print(f"广播消息失败 {connection_id}: {e}")
                disconnected.append(connection_id)

        # 清理断开的连接
        for connection_id in disconnected:
            self.disconnect(connection_id)


# === 增强的聊天请求模型 ===

class EnhancedChatRequest(BaseModel):
    """增强的聊天请求模型，集成会话管理"""
    message: str = Field(..., description="用户消息")
    session_id: Optional[str] = Field(None, description="会话ID，如果为空将创建新会话")
    user_id: str = Field("default_user", description="用户ID")
    stream: bool = Field(False, description="是否流式响应")
    auto_title: bool = Field(True, description="是否自动生成会话标题")


class EnhancedChatResponse(BaseModel):
    """增强的聊天响应模型"""
    message: str = Field(..., description="AI回复")
    session_id: str = Field(..., description="会话ID")
    session_title: Optional[str] = Field(None, description="会话标题")
    user_id: str = Field(..., description="用户ID")
    timestamp: datetime = Field(default_factory=datetime.now, description="时间戳")
    is_new_session: bool = Field(False, description="是否为新创建的会话")


# === 创建增强的FastAPI应用 ===

def create_enhanced_app() -> FastAPI:
    """创建增强的FastAPI应用实例"""
    
    # 获取Web配置
    web_config = config_manager.get_web_config()
    
    app = FastAPI(
        title="LangGraph Agent Enhanced Web API",
        description="基于LangGraph的智能助手增强Web API接口 - 集成完整会话管理",
        version="2.1.0",
        docs_url="/docs",
        redoc_url="/redoc"
    )
    
    # 配置CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=web_config.get('cors_origins', ['*']),
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # 创建WebSocket管理器
    websocket_manager = WebSocketManager()
    
    # === 启动事件 ===
    
    @app.on_event("startup")
    async def startup_event():
        """应用启动时的初始化"""
        print("🚀 启动增强Web API服务...")
        
        # 初始化会话管理API
        await initialize_session_api()
        
        # 初始化agent核心
        await agent_core.initialize()
        
        print("✅ 增强Web API服务启动完成")
    
    # === 基础端点 ===
    
    @app.get("/health", response_model=HealthResponse)
    async def health_check():
        """健康检查"""
        return HealthResponse(
            status="healthy",
            version="2.1.0"
        )
    
    @app.get("/")
    async def root():
        """根端点"""
        return {
            "message": "LangGraph Agent Enhanced Web API",
            "version": "2.1.0",
            "features": [
                "完整会话管理",
                "智能会话命名",
                "用户会话隔离",
                "消息历史管理",
                "实时WebSocket通信"
            ]
        }
    
    # === 增强的聊天端点 ===
    
    @app.post("/api/chat", response_model=EnhancedChatResponse)
    async def enhanced_chat_endpoint(
        request: EnhancedChatRequest,
        data_layer = Depends(get_data_layer),
        naming_service = Depends(get_naming_service)
    ):
        """
        增强的聊天API端点
        
        - 集成完整的会话管理
        - 自动创建用户和会话
        - 智能会话命名
        - 消息历史保存
        """
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            is_new_session = False
            session_id = request.session_id
            session_title = None
            
            # 如果没有提供session_id，创建新会话
            if not session_id:
                # 确保用户存在
                user = await data_layer.get_user_by_identifier(request.user_id)
                if not user:
                    user = await data_layer.create_user(
                        identifier=request.user_id,
                        display_name=f"用户 {request.user_id}",
                        metadata={"auto_created": True}
                    )
                
                # 创建新会话
                session = await data_layer.create_session(
                    user_id=user["id"],
                    title=None,  # 将在第一条消息后自动生成
                    metadata={"created_via": "chat_api"}
                )
                session_id = session["id"]
                is_new_session = True
                print(f"📝 创建新会话: {session_id}")
            
            # 保存用户消息到数据库
            await data_layer.add_message(
                session_id=session_id,
                role="user",
                content=request.message,
                metadata={"source": "chat_api"}
            )
            
            # 处理消息（调用LangGraph Agent）
            response = await agent_core.process_message(
                message=request.message,
                thread_id=session_id  # 使用session_id作为thread_id
            )
            
            # 保存AI回复到数据库
            await data_layer.add_message(
                session_id=session_id,
                role="assistant",
                content=response["response"],
                metadata={"source": "agent_core"}
            )
            
            # 如果是新会话且启用自动标题，生成智能标题
            if is_new_session and request.auto_title:
                session_title = await naming_service.generate_intelligent_title(request.message)
                await data_layer.update_session_title_if_needed(session_id, session_title)
                print(f"🏷️ 生成会话标题: {session_title}")
            else:
                # 获取现有会话标题
                session = await data_layer.get_session(session_id)
                session_title = session["title"] if session else None
            
            return EnhancedChatResponse(
                message=response["response"],
                session_id=session_id,
                session_title=session_title,
                user_id=request.user_id,
                is_new_session=is_new_session
            )
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {
                "endpoint": "enhanced_chat", 
                "request": request.model_dump()
            })
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === 流式聊天端点 ===
    
    @app.post("/api/chat/stream")
    async def enhanced_stream_chat_endpoint(
        request: EnhancedChatRequest,
        data_layer = Depends(get_data_layer)
    ):
        """
        增强的流式聊天端点
        
        - 支持Server-Sent Events (SSE)
        - 实时返回AI生成的内容
        - 集成会话管理
        """
        try:
            # 确保agent已初始化
            await agent_core.initialize()
            
            # 会话管理逻辑（与普通聊天端点相同）
            is_new_session = False
            session_id = request.session_id
            
            if not session_id:
                user = await data_layer.get_user_by_identifier(request.user_id)
                if not user:
                    user = await data_layer.create_user(
                        identifier=request.user_id,
                        display_name=f"用户 {request.user_id}",
                        metadata={"auto_created": True}
                    )
                
                session = await data_layer.create_session(
                    user_id=user["id"],
                    title=None,
                    metadata={"created_via": "stream_chat_api"}
                )
                session_id = session["id"]
                is_new_session = True
            
            # 保存用户消息
            await data_layer.add_message(
                session_id=session_id,
                role="user",
                content=request.message,
                metadata={"source": "stream_chat_api"}
            )
            
            # 流式处理（这里简化实现，实际需要支持真正的流式响应）
            response = await agent_core.process_message(
                message=request.message,
                thread_id=session_id
            )
            
            # 保存AI回复
            await data_layer.add_message(
                session_id=session_id,
                role="assistant",
                content=response["response"],
                metadata={"source": "agent_core_stream"}
            )
            
            return {
                "session_id": session_id,
                "response": response["response"],
                "is_new_session": is_new_session,
                "stream": True
            }
            
        except Exception as e:
            custom_error = error_handler.handle_error(e, {
                "endpoint": "enhanced_stream_chat", 
                "request": request.model_dump()
            })
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === 包含会话管理路由 ===

    app.include_router(session_router, tags=["会话管理"])
    
    # === 工具相关端点 ===
    
    @app.get("/api/tools")
    async def get_available_tools():
        """获取可用工具列表"""
        try:
            await agent_core.initialize()
            tools = agent_core.get_available_tools()
            return {"tools": tools}
        except Exception as e:
            custom_error = error_handler.handle_error(e, {"endpoint": "tools"})
            raise HTTPException(status_code=500, detail=str(custom_error))
    
    # === WebSocket端点 ===
    
    @app.websocket("/ws/{session_id}")
    async def websocket_endpoint(websocket: WebSocket, session_id: str):
        """WebSocket聊天端点 - 支持流式AI响应"""
        connection_id = f"ws_{uuid.uuid4().hex[:8]}"

        try:
            await websocket_manager.connect(websocket, connection_id, session_id)

            # 发送连接确认
            await websocket.send_text(json.dumps({
                "type": "connection",
                "session_id": session_id,
                "message": "WebSocket连接已建立",
                "timestamp": datetime.now().isoformat()
            }))

            while True:
                # 接收消息
                data = await websocket.receive_text()
                message_data = json.loads(data)

                # 处理心跳
                if message_data.get("type") == "ping":
                    await websocket.send_text(json.dumps({"type": "pong"}))
                    continue

                # 处理聊天消息
                if message_data.get("type") == "chat":
                    user_message = message_data.get("message", "")
                    user_id = message_data.get("user_id", "default_user")
                    thread_id = message_data.get("thread_id", session_id)

                    if user_message:
                        try:
                            # 获取依赖
                            data_layer = get_data_layer()

                            # 确保会话存在
                            session = await data_layer.get_session(session_id)
                            if not session:
                                await data_layer.create_session(
                                    user_id=user_id,
                                    title="新对话"
                                )

                            # 保存用户消息
                            await data_layer.add_message(
                                session_id=session_id,
                                role="user",
                                content=user_message,
                                metadata={"source": "websocket"}
                            )

                            # 发送AI思考状态
                            await websocket.send_text(json.dumps({
                                "type": "AIMessage",
                                "content": "",
                                "status": "thinking",
                                "thinking": True,
                                "session_id": session_id,
                                "timestamp": datetime.now().isoformat()
                            }))

                            # 发送AI思考状态
                            await websocket.send_text(json.dumps({
                                "type": "AIMessage",
                                "content": "",
                                "status": "thinking",
                                "thinking": True,
                                "session_id": session_id,
                                "timestamp": datetime.now().isoformat()
                            }))

                            # 流式处理AI响应
                            ai_response = ""
                            async for chunk in agent_core.stream_message(
                                message=user_message,
                                thread_id=thread_id
                            ):
                                if chunk.get("type") == "AIMessage":
                                    content = chunk.get("content", "")
                                    tool_calls = chunk.get("tool_calls")

                                    if tool_calls:
                                        # 工具调用状态
                                        await websocket.send_text(json.dumps({
                                            "type": "AIMessage",
                                            "content": content,
                                            "status": "calling_tools",
                                            "tool_calls": tool_calls,
                                            "thinking": False,
                                            "session_id": session_id,
                                            "timestamp": datetime.now().isoformat()
                                        }))
                                    elif content:
                                        # 响应内容
                                        ai_response = content
                                        await websocket.send_text(json.dumps({
                                            "type": "AIMessage",
                                            "content": content,
                                            "status": "responding",
                                            "thinking": False,
                                            "session_id": session_id,
                                            "timestamp": datetime.now().isoformat()
                                        }))

                            # 发送完成状态
                            await websocket.send_text(json.dumps({
                                "type": "AIMessage",
                                "content": ai_response,
                                "status": "complete",
                                "thinking": False,
                                "session_id": session_id,
                                "timestamp": datetime.now().isoformat()
                            }))

                            # 保存AI响应
                            if ai_response:
                                await data_layer.add_message(
                                    session_id=session_id,
                                    role="assistant",
                                    content=ai_response,
                                    metadata={"source": "websocket_stream"}
                                )

                            # 自动生成会话标题
                            try:
                                session_info = await data_layer.get_session(session_id)
                                if session_info and session_info.get("message_count", 0) == 2:  # 第一轮对话后
                                    # 使用数据层的智能标题生成
                                    await data_layer.update_session_title_if_needed(session_id, user_message)
                            except Exception as e:
                                print(f"自动命名失败: {e}")

                        except Exception as e:
                            print(f"处理消息失败: {e}")
                            await websocket.send_text(json.dumps({
                                "type": "AIMessage",
                                "content": f"抱歉，处理消息时出现错误：{str(e)}",
                                "status": "error",
                                "thinking": False,
                                "session_id": session_id,
                                "timestamp": datetime.now().isoformat()
                            }))

        except WebSocketDisconnect:
            websocket_manager.disconnect(connection_id)
            print(f"WebSocket连接断开: {connection_id}")
        except Exception as e:
            print(f"WebSocket错误: {e}")
            try:
                await websocket.close()
            except:
                pass
            websocket_manager.disconnect(connection_id)
    
    return app


# === 应用实例 ===

# 创建应用实例
enhanced_app = create_enhanced_app()

# 兼容性别名
app = enhanced_app
